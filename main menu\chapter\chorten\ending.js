// Ending page JavaScript

// Sound manager for reward sound
let rewardSound;
const rewardSoundPaths = [
    "../../../Sound effects/Reward unlocked.wav",
    "../../Sound effects/Reward unlocked.wav",
    "../Sound effects/Reward unlocked.wav",
    "./Sound effects/Reward unlocked.wav",
    "Sound effects/Reward unlocked.wav"
];

// Load reward sound
function loadRewardSound() {
    let pathIndex = 0;

    function tryNextPath() {
        if (pathIndex >= rewardSoundPaths.length) {
            console.error('Could not load reward sound from any path');
            return;
        }

        rewardSound = new Audio(rewardSoundPaths[pathIndex]);
        rewardSound.preload = "auto";
        rewardSound.volume = 0.8;

        rewardSound.addEventListener('canplaythrough', () => {
            console.log(`Reward sound loaded successfully from: ${rewardSoundPaths[pathIndex]}`);
        });

        rewardSound.addEventListener('error', (e) => {
            console.log(`Failed to load reward sound from path ${pathIndex}: ${rewardSoundPaths[pathIndex]}`);
            pathIndex++;
            tryNextPath();
        });
    }

    tryNextPath();
}

// Play reward sound
function playRewardSound() {
    if (rewardSound) {
        rewardSound.currentTime = 0;
        rewardSound.play().then(() => {
            console.log('Reward sound played successfully');
        }).catch(error => {
            console.log('Could not play reward sound:', error);
        });
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Load reward sound
    loadRewardSound();
    // Fade out black screen slowly after page loads
    const blackScreen = document.getElementById('black-screen-overlay');
    if (blackScreen) {
        setTimeout(() => {
            blackScreen.classList.add('fade-out');
            // Remove the overlay after fade completes
            setTimeout(() => {
                blackScreen.remove();
                // Play reward sound when the congratulations screen is fully visible
                setTimeout(() => {
                    playRewardSound();
                }, 500);
            }, 2000);
        }, 500);
    }

    // Create sparkle effects around the relic
    createSparkleEffect();

    // Add click interaction to the relic
    const relicImage = document.querySelector('.relic-image');
    if (relicImage) {
        relicImage.addEventListener('click', function() {
            createBurstEffect(this);
        });
    }

    // Add click handler for next button
    const nextButton = document.getElementById('next-button');
    if (nextButton) {
        nextButton.addEventListener('click', function() {
            redirectToArrival();
        });
    }

    // Enable audio context on first user interaction (for browser autoplay policies)
    document.addEventListener('click', function enableAudio() {
        if (rewardSound) {
            // Try to play and immediately pause to enable audio context
            rewardSound.play().then(() => {
                rewardSound.pause();
                rewardSound.currentTime = 0;
            }).catch(() => {
                // Ignore errors during audio context initialization
            });
        }
        // Remove this listener after first interaction
        document.removeEventListener('click', enableAudio);
    }, { once: true });
});

// Create continuous sparkle effect
function createSparkleEffect() {
    const relicContainer = document.querySelector('.relic-container');
    if (!relicContainer) return;
    
    setInterval(() => {
        createSparkle(relicContainer);
    }, 500);
}

// Create individual sparkle
function createSparkle(container) {
    const sparkle = document.createElement('div');
    sparkle.className = 'sparkle';
    
    // Random position around the relic
    const rect = container.getBoundingClientRect();
    const x = Math.random() * rect.width;
    const y = Math.random() * rect.height;
    
    sparkle.style.cssText = `
        position: absolute;
        left: ${x}px;
        top: ${y}px;
        width: 4px;
        height: 4px;
        background: #FFD700;
        border-radius: 50%;
        pointer-events: none;
        animation: sparkleAnimation 1.5s ease-out forwards;
        box-shadow: 0 0 6px #FFD700;
    `;
    
    container.appendChild(sparkle);
    
    // Remove sparkle after animation
    setTimeout(() => {
        if (sparkle.parentNode) {
            sparkle.parentNode.removeChild(sparkle);
        }
    }, 1500);
}

// Create burst effect when relic is clicked
function createBurstEffect(element) {
    const rect = element.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    // Create multiple particles
    for (let i = 0; i < 20; i++) {
        setTimeout(() => {
            createBurstParticle(centerX, centerY);
        }, i * 50);
    }
}

// Create individual burst particle
function createBurstParticle(centerX, centerY) {
    const particle = document.createElement('div');
    
    // Random direction and distance
    const angle = Math.random() * Math.PI * 2;
    const distance = 50 + Math.random() * 100;
    const tx = Math.cos(angle) * distance;
    const ty = Math.sin(angle) * distance;
    
    particle.style.cssText = `
        position: fixed;
        left: ${centerX}px;
        top: ${centerY}px;
        width: 6px;
        height: 6px;
        background: #FFD700;
        border-radius: 50%;
        pointer-events: none;
        z-index: 1000;
        box-shadow: 0 0 10px #FFD700;
        animation: burstAnimation 2s ease-out forwards;
        --tx: ${tx}px;
        --ty: ${ty}px;
    `;
    
    document.body.appendChild(particle);
    
    // Remove particle after animation
    setTimeout(() => {
        if (particle.parentNode) {
            particle.parentNode.removeChild(particle);
        }
    }, 2000);
}

// Add CSS animations dynamically
const style = document.createElement('style');
style.textContent = `
    @keyframes sparkleAnimation {
        0% {
            opacity: 0;
            transform: scale(0);
        }
        50% {
            opacity: 1;
            transform: scale(1);
        }
        100% {
            opacity: 0;
            transform: scale(0);
        }
    }
    
    @keyframes burstAnimation {
        0% {
            opacity: 1;
            transform: translate(0, 0) scale(1);
        }
        100% {
            opacity: 0;
            transform: translate(var(--tx), var(--ty)) scale(0);
        }
    }
`;
document.head.appendChild(style);

// Function to handle redirect to arrival with black screen transition
function redirectToArrival() {
    // Create black screen overlay for transition
    const fadeOverlay = document.createElement('div');
    fadeOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: black;
        opacity: 0;
        z-index: 10000;
        transition: opacity 1.5s ease-in-out;
        pointer-events: none;
    `;
    document.body.appendChild(fadeOverlay);

    // Trigger fade in
    setTimeout(() => {
        fadeOverlay.style.opacity = '1';
    }, 50);

    // Redirect after fade completes
    setTimeout(() => {
        window.location.href = '../arrival/arrival.html';
    }, 1800); // 1.8 seconds to allow fade to complete
}
