document.addEventListener('DOMContentLoaded', () => {
    // Handle black screen fade-out on page load
    const blackScreen = document.getElementById('black-screen-overlay');
    if (blackScreen) {
        setTimeout(() => {
            blackScreen.style.opacity = '0';
            // Remove the overlay after fade completes
            setTimeout(() => {
                blackScreen.remove();
            }, 2000);
        }, 500);
    }

    const girl = document.getElementById('girl');
    const treasure = document.getElementById('treasure');
    const demon = document.getElementById('demon');
    const gameContainer = document.getElementById('game-container');

    // Get the initial positions (in case they are set in CSS and we need to modify them)
    let girlX = -50; // Initial left position for girl
    let treasureX = -20; // Initial left position for treasure
    let demonX = gameContainer.offsetWidth - demon.offsetWidth - 120; // Initial left for demon based on 'right' CSS

    const girlSpeed = 2; // Pixels per frame
    const demonSpeed = 1.5; // Pixels per frame

    // Target positions (adjust these values based on where "middle" is in your scene)
    const girlTargetX = (gameContainer.offsetWidth / 2) - (girl.offsetWidth / 2) - 50; // A bit to the left of true middle
    const demonTargetX = (gameContainer.offsetWidth / 2) - (demon.offsetWidth / 2) + 50; // A bit to the right of true middle

    let animationId; // To store the requestAnimationFrame ID

    function animate() {
        // Move girl and treasure
        if (girlX < girlTargetX) {
            girlX += girlSpeed;
            treasureX += girlSpeed; // Treasure moves with the girl
            girl.style.left = `${girlX}px`;
            treasure.style.left = `${treasureX}px`;
        }

        // Move demon
        if (demonX > demonTargetX) {
            demonX -= demonSpeed;
            demon.style.left = `${demonX}px`;
        }

        // Check if all animations have reached their targets
        if (girlX >= girlTargetX && demonX <= demonTargetX) {
            // Stop animation if both have reached their destinations
            cancelAnimationFrame(animationId);
            console.log('Animation finished!');

            // Show continue button with fade-in after animation completes
            setTimeout(() => {
                const continueButton = document.getElementById('continue-button');
                continueButton.classList.add('show');
            }, 1000); // Wait 1 second before showing button
        } else {
            // Continue animation
            animationId = requestAnimationFrame(animate);
        }
    }

    // Start the animation when the page loads
    animate();

    // Add event listener for continue button
    const continueButton = document.getElementById('continue-button');
    continueButton.addEventListener('click', () => {
        window.location.href = 'sinmo.html';
    });
});