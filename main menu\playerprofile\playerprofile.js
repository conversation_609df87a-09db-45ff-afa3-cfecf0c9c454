// Sound manager for menu clicks
let menuClickSound;
const menuSoundPaths = [
    "../../Sound effects/Click menu and settings .mp3",
    "../Sound effects/Click menu and settings .mp3",
    "./Sound effects/Click menu and settings .mp3",
    "../../Sound effects/click-menu.mp3",
    "../Sound effects/click-menu.mp3",
    "./Sound effects/click-menu.mp3"
];

// Load menu click sound
function loadMenuClickSound() {
    let pathIndex = 0;

    function tryNextPath() {
        if (pathIndex >= menuSoundPaths.length) {
            console.error('Could not load menu click sound from any path');
            return;
        }

        menuClickSound = new Audio(menuSoundPaths[pathIndex]);
        menuClickSound.preload = "auto";
        menuClickSound.volume = 0.7;

        menuClickSound.addEventListener('canplaythrough', () => {
            console.log(`Menu click sound loaded successfully from: ${menuSoundPaths[pathIndex]}`);
        });

        menuClickSound.addEventListener('error', (e) => {
            console.log(`Failed to load menu sound from path ${pathIndex}: ${menuSoundPaths[pathIndex]}`);
            pathIndex++;
            tryNextPath();
        });
    }

    tryNextPath();
}

// Play menu click sound
function playMenuClickSound() {
    if (menuClickSound) {
        menuClickSound.currentTime = 0;
        menuClickSound.play().catch(error => {
            console.log('Could not play menu click sound:', error);
        });
    }
}

document.addEventListener('DOMContentLoaded', () => {
  // Load menu click sound
  loadMenuClickSound();

  const editBtn = document.querySelector('.edit-btn');
  const imageUpload = document.getElementById('image-upload');
  const profileImg = document.getElementById('profile-img');
  const playerNameInput = document.getElementById('player-name');
  const DEFAULT_IMAGE = 'img/character design female 2.png';

  // Always set to default image on load
  profileImg.src = DEFAULT_IMAGE;

  // Clicking the edit button triggers the file input
  editBtn.addEventListener('click', () => {
    playMenuClickSound(); // Play click sound for edit button
    imageUpload.click();
  });

  // Handle image selection
  imageUpload.addEventListener('change', (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        profileImg.src = e.target.result;
      };
      
      reader.readAsDataURL(file);
    }
  });

  // Create segmented bars
  function createSegmentedBar(containerId, segmentCount, activeCount, className) {
    const container = document.getElementById(containerId);
    container.innerHTML = '';
    
    for (let i = 0; i < segmentCount; i++) {
      const segment = document.createElement('div');
      segment.className = `bar-segment ${i < activeCount ? 'active' : ''}`;
      segment.style.setProperty('--segment-index', i);
      container.appendChild(segment);
    }
  }

  // Name input handling
  playerNameInput.addEventListener('focus', () => {
    if (playerNameInput.value === 'Player Name') {
      playerNameInput.value = '';
      playerNameInput.placeholder = 'Player Name';
    }
  });

  playerNameInput.addEventListener('blur', () => {
    if (playerNameInput.value === '') {
      playerNameInput.placeholder = '';
      playerNameInput.value = 'Player Name';
    }
  });

  // Initialize bars (9/10 health, 7/10 mana)
  createSegmentedBar('health-bar', 10, 9, 'health');
  createSegmentedBar('mana-bar', 10, 7, 'mana');
});