
:root {
  --bg-color: #1a1a1a;
  --stripe-color: #222;
  --panel-border: #aaa;
  --panel-bg: #666;
  --panel-inner-bg: #4f4f4f;
  --header-bg: #555;
  --tab-active-bg: #4f4f4f;
  --tab-inactive-bg: #333;
  --text-color: #f0f0f0;
  --accent-green: #39ff14;
  --accent-red: #ff3b3b;
  --accent-border: #39ff14;
  --button-hover-shadow: rgba(57, 255, 20, 0.6);
  --polygon-color: #ffd700;
  --font-pixel: "Press Start 2P", cursive;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html,
body {
  height: 100%;
  width: 100%;
  background-color: var(--bg-color);
  color: var(--text-color);
  font-family: var(--font-pixel);
  overflow-x: hidden;
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

/* -------------------------------------------------------
   BACKGROUND STRIPES + ANIMATION
------------------------------------------------------- */
.background {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background-image: url('backg 1.png');
  background-size: cover;
  background-repeat: repeat;
  animation: scroll-background 30s linear infinite;
}

@keyframes scroll-background {
  from {
    background-position: 0 0;
  }
  to {
    background-position: 0 1000px;
  }
}

    
/* -------------------------------------------------------
   SETTINGS PANEL CONTAINER
------------------------------------------------------- */
.panel {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-width: 1460px;
  width: 90%;
  max-height: 90vh;
  min-height: 400px; /* Ensure minimum height for save button positioning */
  background-color: var(--panel-bg);
  border: 4px solid var(--panel-border);
  border-radius: 8px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.8);
  z-index: 2;
  animation: fade-in 1s ease-out forwards;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Prevent panel itself from scrolling */
}

/* Panel Header (non-scrollable) */
.panel-header {
  flex-shrink: 0; /* Don't shrink */
  background-color: var(--panel-bg);
}

/* Header top section with back button and title */
.header-top {
  display: flex;
  align-items: center;
  position: relative;
  padding: 16px;
  background-color: var(--header-bg);
  border-bottom: 2px solid var(--panel-border);
}

/* Back button styling */
.back-button {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  background-color: #00bfff;
  color: black;
  border: 3px solid #000;
  padding: 12px 20px;
  font-family: var(--font-pixel);
  font-size: 0.8rem;
  cursor: pointer;
  border-radius: 25px;
  transition: all 0.2s ease;
  text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.5);
  box-shadow:
      inset 2px 2px 0 rgba(255, 255, 255, 0.3),
      inset -2px -2px 0 rgba(0, 0, 0, 0.3),
      0 4px 8px rgba(0, 0, 0, 0.3);
  letter-spacing: 1px;
}

.back-button:hover {
  background-color: #1e90ff;
  transform: translateY(-50%) translateY(-2px);
  box-shadow:
      inset 2px 2px 0 rgba(255, 255, 255, 0.4),
      inset -2px -2px 0 rgba(0, 0, 0, 0.4),
      0 6px 12px rgba(0, 0, 0, 0.4);
}

.back-button:active {
  transform: translateY(-50%) translateY(0px);
  box-shadow:
      inset 2px 2px 0 rgba(255, 255, 255, 0.2),
      inset -2px -2px 0 rgba(0, 0, 0, 0.5),
      0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Panel Content (scrollable) */
.panel-content {
  flex: 1;
  overflow-y: auto;
  background-color: var(--panel-inner-bg);
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) translateY(0);
  }
}

/* -------------------------------------------------------
   PANEL HEADER (TITLE)
------------------------------------------------------- */
/* .panel h1 {
  margin: 0;
  padding: 16px;
  text-align: center;
   text-shadow:
   2px 2px 0 #000,
   -2px 2px 0 #000,
   2px -2px 0 #000,
   -2px -2px 0 #000,
   2px 0 0 #000,
   -2px 0 0 #000,
   0 2px 0 #000,
   0 -2px 0 #000; ;
  font-size: 2.5rem;
  margin-bottom: 2rem;
  background-color: var
  
 
  (--header-bg);
  border-bottom: 2px solid var(--panel-border);
  letter-spacing: 2px;
  animation: header-glow 2s ease-in-out infinite alternate;
}

@keyframes header-glow {
  from {
    text-shadow: 0 0 2px rgba(255, 255, 255, 0.4);
  }
  to {
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.8);
  }
} */
.panel-header h1 {
  margin: 0;
  flex: 1;
  text-align: center;
  font-size: 2.5rem;
  letter-spacing: 2px;

  /* Layered, directional shadows for a solid 3D look */
  text-shadow:
    1px 1px 0 #000,
    2px 2px 0 #000,
    3px 3px 0 #000,
    4px 4px 0 #000;

  animation: header-glow 2s ease-in-out infinite alternate;
}

@keyframes header-glow {
  from {
    text-shadow:
      1px 1px 0 #000,
      2px 2px 0 #000,
      3px 3px 0 #000,
      4px 4px 0 #000,
      /* subtle white glow on top of the 3D shadow */
      0 0 4px rgba(255, 255, 255, 0.4);
  }
  to {
    text-shadow:
      1px 1px 0 #000,
      2px 2px 0 #000,
      3px 3px 0 #000,
      4px 4px 0 #000,
      0 0 8px rgba(255, 255, 255, 0.8);
  }
}

/* 
-------------------------------------------------------
   TABS (Audio, Controls, Gameplay)
------------------------------------------------------- */
/* .tabs {
  display: flex;
  justify-content: center;
  background-color: var(--tab-inactive-bg);
  border-bottom: 2px solid var(--panel-border);
}

.tab {
  padding: 8px 24px;
  cursor: pointer;
  color: var(--text-color);
  transition: background 0.3s;
  position: relative;
}

.tab:not(.active):hover {
  background-color: var(--header-bg);
  animation: tab-hover-glow 1s ease-in-out infinite alternate;
}

.tab.active {
  background-color: var(--tab-active-bg);
  border-bottom: 4px solid var(--accent-green);
}

@keyframes tab-hover-glow {
  from {
    box-shadow: 0 0 4px rgba(57, 255, 20, 0.3);
  }
  to {
    box-shadow: 0 0 12px rgba(57, 255, 20, 0.8);
  }
} */
.tabs {
  display: flex;
  justify-content: center;
  gap: 100px; /* space between tabs */
  padding: 16px 0;
  background-color: var(--tab-inactive-bg);
  border-bottom: 2px solid var(--panel-border);
}

.tab {
  /* Keep existing styles */
  width: 185px;
  height: 58px;
  background-image: url('Group 31.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;

  /* Center text */
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;

  margin: 0; /* Remove any extra spacing */
  flex-shrink: 0; /* Prevent squishing on small screens */
  color: white;
  cursor: pointer;
  transition: text-shadow 0.3s;
  
  /* Drop shadow for inactive */
  text-shadow:
    1px 1px 0 #0c6,
    2px 2px 0 #0c6,
    3px 3px 0 #0c6;
}

.tab.active {
  background-image: url('Group 30.png'); /* or a different active image */
  text-shadow:
    1px 1px 0 #f36,
    2px 2px 0 #f36,
    3px 3px 0 #f36;
}
.tab:hover {
  text-shadow:
    1px 1px 0 #006633,
    2px 2px 0 #006633,
    3px 3px 0 #006633;

  transform: scale(1.05); /* Slight zoom on hover */
  transition: transform 0.3s, text-shadow 0.3s;

  /* Optional image effect */
  filter: brightness(1.1);
}

/* -------------------------------------------------------
   PANEL CONTENT AREA
------------------------------------------------------- */
.content {
  display: flex;
  padding: 24px 24px 80px 24px; /* Add bottom padding for save button space */
  background-color: var(--panel-inner-bg);
  gap: 180px;
  flex-wrap: wrap;
  justify-content: center;
  align-items: flex-start;
  min-height: fit-content;
}

/* TAB CONTENT SECTIONS */
.tab-content {
  width: 100%;
  display: flex;
  gap: 60px;
  flex-wrap: wrap;
  justify-content: center;
  align-items: flex-start;
  min-height: fit-content;
  padding: 0 0 80px 0; /* Add bottom padding for save button space */
}



#tab-gameplay .sliders-left,
#tab-gameplay .sliders-right {
  flex: 1 1 45%;
  min-width: 300px;
}

/* SLIDER GROUP COMMON */
.slider-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding: 18px 24px;
  background-color: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(170, 170, 170, 0.3);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  box-shadow:
    0 4px 8px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-height: 70px;
}

.slider-group::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(57, 255, 20, 0.1),
    transparent
  );
  transition: left 0.6s ease;
}

.slider-group:hover {
  background-color: rgba(255, 255, 255, 0.08);
  border-color: var(--accent-green);
  box-shadow:
    0 6px 12px rgba(0, 0, 0, 0.4),
    0 0 20px rgba(57, 255, 20, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.slider-group:hover::before {
  left: 100%;
}

.label-container {
  flex: 0 0 150px;
  padding: 12px 16px;
  background-color: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  margin-right: 32px;
  backdrop-filter: blur(5px);
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.slider-group:hover .label-container {
  background-color: rgba(0, 0, 0, 0.4);
  border-color: rgba(57, 255, 20, 0.5);
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    0 0 8px rgba(57, 255, 20, 0.3);
}

.slider-group label {
  font-size: 0.8rem;
  letter-spacing: 1px;
  margin: 0;
  display: block;
  text-align: center;
  font-family: var(--font-pixel);
  font-weight: bold;
  color: var(--text-color);
  text-shadow:
    -1px -1px 0 #000,
    1px -1px 0 #000,
    -1px 1px 0 #000,
    1px 1px 0 #000,
    -2px 0 0 #000,
    2px 0 0 #000,
    0 -2px 0 #000,
    0 2px 0 #000;
}

.slider {
  -webkit-appearance: none;
  width: 180px;
  height: 8px;
  border-radius: 4px;
  margin: 0 24px;
  transition: height 0.3s ease;
  flex: 1;
  max-width: 200px;
}

.slider:focus {
  outline: none;
}

.slider:hover {
  height: 12px;
}

/* GREEN SLIDER (Master, Music, SFX) */
.green-slider {
  background: linear-gradient(
      to right,
      var(--accent-green) 0%,
      var(--accent-green) 100%
    )
    no-repeat center;
  background-size: 100% 100%;
}

/* RED SLIDER (Menu Sounds) */
.red-slider {
  background: linear-gradient(
      to right,
      var(--accent-red) 0%,
      var(--accent-red) 100%
    )
    no-repeat center;
  background-size: 100% 100%;
}

/* SLIDER THUMB STYLES */
.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #fff;
  border: 2px solid var(--panel-border);
  cursor: pointer;
  transition: transform 0.2s ease, background-color 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  background-color: #eee;
}

.slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #fff;
  border: 2px solid var(--panel-border);
  cursor: pointer;
  transition: transform 0.2s ease, background-color 0.2s ease;
}

.slider::-moz-range-thumb:hover {
  transform: scale(1.2);
  background-color: #eee;
}

/* SLIDER VALUE DISPLAY */
.value {
  flex: 0 0 40px;
  font-size: 0.8rem;
  text-align: center;
  font-weight: bold;
  margin-left: 20px;
  animation: value-pulse 1s ease-in-out infinite alternate;
}

@keyframes value-pulse {
  from {
    color: var(--text-color);
  }
  to {
    color: var(--accent-green);
  }
}

/* -------------------------------------------------------
   DROPDOWN COMPONENTS (for Gameplay Settings)
------------------------------------------------------- */
.dropdown-container {
  flex: 1;
  max-width: 200px;
  margin: 0 24px;
}

.dropdown {
  width: 100%;
  height: 40px;
  padding: 8px 12px;
  background-color: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(170, 170, 170, 0.3);
  border-radius: 6px;
  color: var(--text-color);
  font-family: var(--font-pixel);
  font-size: 0.8rem;
  backdrop-filter: blur(5px);
  cursor: pointer;
  transition: all 0.3s ease;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 8px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 32px;
}

.dropdown:hover {
  background-color: rgba(255, 255, 255, 0.08);
  border-color: var(--accent-green);
  box-shadow: 0 0 8px rgba(57, 255, 20, 0.3);
}

.dropdown:focus {
  outline: none;
  border-color: var(--accent-green);
  box-shadow: 0 0 12px rgba(57, 255, 20, 0.5);
}

.green-dropdown {
  border-color: rgba(57, 255, 20, 0.5);
}

.red-dropdown {
  border-color: rgba(255, 0, 0, 0.5);
}

.red-dropdown:hover {
  border-color: var(--accent-red);
  box-shadow: 0 0 8px rgba(255, 0, 0, 0.3);
}

.red-dropdown:focus {
  border-color: var(--accent-red);
  box-shadow: 0 0 12px rgba(255, 0, 0, 0.5);
}

/* Dropdown option styling for visibility */
.dropdown option {
  background-color: #666;
  color: #fff;
  padding: 8px 12px;
  font-family: var(--font-pixel);
  font-size: 0.8rem;
}

.dropdown option:hover {
  background-color: #888;
}

.dropdown option:checked {
  background-color: #fff;
  color: #000;
}

/* -------------------------------------------------------
   TOGGLE BUTTON COMPONENTS (for Gameplay Settings)
------------------------------------------------------- */
.toggle-container {
  flex: 1;
  max-width: 200px;
  margin: 0 24px;
}

.toggle-button {
  width: 100%;
  height: 40px;
  padding: 8px 16px;
  background-color: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(170, 170, 170, 0.3);
  border-radius: 6px;
  color: var(--text-color);
  font-family: var(--font-pixel);
  font-size: 0.8rem;
  backdrop-filter: blur(5px);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.toggle-button:hover {
  background-color: rgba(255, 255, 255, 0.08);
  transform: translateY(-1px);
}

.toggle-text {
  font-weight: bold;
  text-transform: uppercase;
}

.toggle-indicator {
  width: 12px;
  height: 12px;
  border: 2px solid currentColor;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.toggle-button.active .toggle-indicator {
  background-color: currentColor;
  box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.8);
}

.green-toggle {
  border-color: rgba(57, 255, 20, 0.5);
  color: var(--accent-green);
}

.green-toggle:hover {
  border-color: var(--accent-green);
  box-shadow: 0 0 8px rgba(57, 255, 20, 0.3);
}

.red-toggle {
  border-color: rgba(255, 0, 0, 0.5);
  color: var(--accent-red);
}

.red-toggle:hover {
  border-color: var(--accent-red);
  box-shadow: 0 0 8px rgba(255, 0, 0, 0.3);
}

/* -------------------------------------------------------
   CONTROLS LAYOUT
------------------------------------------------------- */
.controls-layout {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
  gap: 60px;
}

.controls-left,
.controls-right {
  flex: 0 0 250px;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.controller-diagram {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  max-width: 500px;
}

.controller-diagram img {
  width: 100%;
  height: auto;
  max-width: 450px;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

/* -------------------------------------------------------
   CONTROL ITEMS (Left Side)
------------------------------------------------------- */
.control-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px 20px;
  background-color: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(170, 170, 170, 0.3);
  border-radius: 6px;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
  min-height: 70px;
}

.control-item:hover {
  background-color: rgba(255, 255, 255, 0.08);
  border-color: var(--accent-red);
  box-shadow: 0 0 8px rgba(255, 0, 0, 0.3);
}

.control-label {
  color: var(--text-color);
  font-family: var(--font-pixel);
  font-size: 0.9rem;
  font-weight: bold;
  flex: 1;
  text-align: left;
  line-height: 1.4;
  letter-spacing: 0.5px;
  text-shadow:
    -1px -1px 0 #000,
    1px -1px 0 #000,
    -1px 1px 0 #000,
    1px 1px 0 #000,
    -2px 0 0 #000,
    2px 0 0 #000,
    0 -2px 0 #000,
    0 2px 0 #000;
}

/* Control Switches */
.control-switch {
  width: 50px;
  height: 24px;
  background-color: rgba(0, 0, 0, 0.4);
  border: 2px solid var(--accent-red);
  border-radius: 12px;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.control-switch:hover {
  box-shadow:
    0 0 8px rgba(255, 0, 0, 0.4),
    0 4px 8px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.switch-button {
  width: 18px;
  height: 18px;
  background-color: var(--accent-red);
  border-radius: 50%;
  position: absolute;
  top: 1px;
  left: 2px;
  transition: all 0.3s ease;
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.control-switch.active .switch-button {
  left: 28px;
  background-color: var(--accent-green);
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.3),
    0 0 8px rgba(57, 255, 20, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.control-switch.active {
  border-color: var(--accent-green);
  background-color: rgba(57, 255, 20, 0.1);
  box-shadow:
    0 0 8px rgba(57, 255, 20, 0.3),
    0 2px 4px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.control-switch.active:hover {
  box-shadow:
    0 0 12px rgba(57, 255, 20, 0.5),
    0 4px 8px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Red Switch Variant */
.red-switch {
  border-color: var(--accent-red);
}

.red-switch .switch-button {
  background-color: var(--accent-red);
}

.red-switch:hover {
  box-shadow:
    0 0 8px rgba(255, 59, 59, 0.4),
    0 4px 8px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Control Buttons */
.control-button {
  width: 32px;
  height: 32px;
  background-color: var(--accent-red);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  color: white;
  font-family: var(--font-pixel);
  font-size: 1rem;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(5px);
  text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.5);
}

.control-button:hover {
  transform: translateY(-2px);
  box-shadow:
    0 4px 8px rgba(255, 0, 0, 0.4),
    0 6px 12px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  background-color: #ff5555;
}

.control-button:active {
  transform: translateY(0);
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Red Button Variant */
.red-button {
  background-color: var(--accent-red);
  border-color: rgba(255, 255, 255, 0.3);
}

.red-button:hover {
  background-color: #ff5555;
  box-shadow:
    0 4px 8px rgba(255, 59, 59, 0.4),
    0 6px 12px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* -------------------------------------------------------
   ACTION BUTTONS (Right Side)
------------------------------------------------------- */
.action-button {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 18px 20px;
  background-color: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(170, 170, 170, 0.3);
  border-radius: 6px;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
  cursor: pointer;
  min-height: 70px;
}

.action-button:hover {
  background-color: rgba(255, 255, 255, 0.08);
  border-color: var(--accent-green);
  box-shadow: 0 0 8px rgba(57, 255, 20, 0.3);
}

.action-button-label {
  width: 32px;
  height: 32px;
  background-color: var(--accent-green);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  color: white;
  font-family: var(--font-pixel);
  font-size: 1rem;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(5px);
  text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.5);
}

.action-button:hover .action-button-label {
  transform: translateY(-2px);
  box-shadow:
    0 4px 8px rgba(57, 255, 20, 0.4),
    0 6px 12px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  background-color: #4dff2a;
}

.action-button:active .action-button-label {
  transform: translateY(0);
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Green Button Variant */
.green-button {
  background-color: var(--accent-green);
  border-color: rgba(255, 255, 255, 0.3);
}

.green-button:hover {
  background-color: #4dff2a;
  box-shadow:
    0 4px 8px rgba(57, 255, 20, 0.4),
    0 6px 12px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.action-text {
  color: var(--text-color);
  font-family: var(--font-pixel);
  font-size: 0.9rem;
  font-weight: bold;
  text-transform: uppercase;
  flex: 1;
  text-align: left;
  line-height: 1.4;
  letter-spacing: 0.5px;
  text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.5);
  transition: color 0.3s ease;
}

.action-button:hover .action-text {
  color: var(--accent-green);
  text-shadow:
    1px 1px 0 rgba(0, 0, 0, 0.5),
    0 0 4px rgba(57, 255, 20, 0.3);
}

/* -------------------------------------------------------
   RESPONSIVE CONTROLS
------------------------------------------------------- */
@media (max-width: 1024px) {
  .controls-layout {
    flex-direction: column;
    gap: 40px;
    padding: 30px 20px;
  }

  .controls-left,
  .controls-right {
    flex: none;
    width: 100%;
    max-width: 400px;
  }

  .controller-diagram {
    order: -1;
    max-width: 350px;
  }
}

@media (max-width: 768px) {
  .controls-layout {
    gap: 30px;
    padding: 20px 15px;
  }

  .control-item,
  .action-button {
    padding: 10px 14px;
  }

  .controller-diagram img {
    max-width: 300px;
  }

  .control-label,
  .action-text {
    font-size: 0.8rem;
  }
}

/* LEFT AND RIGHT SLIDERS CONTAINERS */
.sliders-left,
.sliders-right {
  flex: 1 1 400px;
  max-width: 500px;
  min-width: 350px;
}

/* Ensure both columns have equal height alignment */
.sliders-left {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.sliders-right {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

/* -------------------------------------------------------
   SAVE BUTTON
------------------------------------------------------- */
.save-image-button {
  position: absolute !important;
  bottom: 20px !important;
  right: 20px !important;
  display: block;
  padding: 0;
  background-color: transparent;
  border: none;
  cursor: pointer;
  z-index: 1000;
  animation: button-glow 1.5s ease-in-out infinite alternate;
  width: auto;
  height: auto;
  /* Ensure button stays visible even when panel content scrolls */
  pointer-events: auto;
  /* Initially hidden */
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

/* Show save button when scrolled to bottom */
.save-image-button.show {
  opacity: 1;
  visibility: visible;
}

/* Style the image inside the button */
.save-image-button img {
  display: block;
  width: 150px;
  height: 38px;
  object-fit: contain;
  transition: transform 0.2s ease, box-shadow 0.3s ease;
}

/* Optional hover effect for the image */
.save-image-button:hover img {
  transform: scale(1.05);
  box-shadow: 0 0 12px var(--button-hover-shadow),
              0 0 20px var(--button-hover-shadow);
}

/* Button glow animation */
@keyframes button-glow {
  from {
    filter: drop-shadow(0 0 5px rgba(57, 255, 20, 0.3));
  }
  to {
    filter: drop-shadow(0 0 15px rgba(57, 255, 20, 0.6));
  }
}

/* Ensure save button stays at bottom right on all screen sizes */
@media (max-width: 768px) {
  .save-image-button {
    bottom: 15px !important;
    right: 15px !important;
  }
  .save-image-button img {
    width: 120px;
    height: 30px;
  }
}

@media (max-width: 480px) {
  .save-image-button {
    bottom: 10px !important;
    right: 10px !important;
  }
  .save-image-button img {
    width: 100px;
    height: 25px;
  }
}

/* -------------------------------------------------------
   RESPONSIVE DESIGN
------------------------------------------------------- */
@media (max-width: 768px) {
  .content {
    flex-direction: column;
    gap: 24px;
    justify-content: center;
    padding: 24px;
  }
  .sliders-left,
  .sliders-right {
    flex: 1 1 100%;
    max-width: none;
    min-width: auto;
  }
  .slider-group {
    padding: 16px 20px;
    margin-bottom: 20px;
    min-height: 60px;
  }
  .slider {
    width: 150px;
    margin: 0 12px;
  }
  .label-container {
    flex: 0 0 120px;
    padding: 10px 12px;
    margin-right: 16px;
  }
  .value {
    flex: 0 0 40px;
    margin-left: 6px;
  }
  .dropdown-container,
  .toggle-container {
    margin: 0 12px;
  }
}

@media (max-width: 480px) {
  .panel {
    margin: 40px 10px 20px 10px;
  }
  .panel h1 {
    font-size: 1rem;
    padding: 12px;
  }
  .tab {
    padding: 6px 12px;
    font-size: 0.7rem;
  }
  .slider-group {
    padding: 10px 12px;
    margin-bottom: 12px;
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  .slider-group label {
    font-size: 0.7rem;
  }
  .label-container {
    flex: none;
    padding: 6px 8px;
    margin-right: 0;
    margin-bottom: 8px;
    text-align: center;
  }
  .slider {
    margin: 0;
  }
  .value {
    font-size: 0.7rem;
    text-align: center;
    margin-top: 4px;
  }
  /* Removed conflicting save-button class to prevent interference with save-image-button */
}

/* Redundant styles removed - using main control styles above */
