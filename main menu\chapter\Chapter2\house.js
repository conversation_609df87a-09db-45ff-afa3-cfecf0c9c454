const canvas = document.getElementById("gameCanvas");
const ctx = canvas.getContext("2d");

// Load audio with multiple path attempts
let doorOpenSound;
const soundPaths = [
  "../../../Sound effects/Door open.mp3",
  "../../Sound effects/Door open.mp3",
  "../Sound effects/Door open.mp3"
];

// Try to load the sound from different paths
function loadDoorSound() {
  let pathIndex = 0;

  function tryNextPath() {
    if (pathIndex >= soundPaths.length) {
      console.error('Could not load door open sound from any path');
      return;
    }

    doorOpenSound = new Audio(soundPaths[pathIndex]);
    doorOpenSound.preload = "auto";
    doorOpenSound.volume = 0.7;

    doorOpenSound.addEventListener('canplaythrough', () => {
      console.log(`Door open sound loaded successfully from: ${soundPaths[pathIndex]}`);
    });

    doorOpenSound.addEventListener('error', (e) => {
      console.log(`Failed to load from path ${pathIndex}: ${soundPaths[pathIndex]}`);
      pathIndex++;
      tryNextPath();
    });
  }

  tryNextPath();
}

loadDoorSound();

// Load images
const bgImage = new Image();
bgImage.src = "house.png";

const ladyFrontImage = new Image();
ladyFrontImage.src = "female character1.png"; // Front view

const ladyBackImage = new Image();
ladyBackImage.src = "lady back view.png"; // Back view

const lady = {
  x: 70,
  y: 300,
  width: 32,
  height: 94,
  speed: 2,
  direction: 1, // 1 = right (front), -1 = left (back)
  state: "walking", // walking, turning, returning
  turnFrames: 0,
  maxTurnFrames: 30, // frames for turn animation
  doorSoundPlayed: false // flag to ensure sound plays only once
};

function drawLady() {
  ctx.save();
  ctx.translate(lady.x + lady.width / 2, lady.y + lady.height / 2);
  
  // Choose the appropriate image based on direction
  const img = lady.direction === 1 ? ladyFrontImage : ladyBackImage;
  
  // Draw the image centered
  ctx.drawImage(
    img,
    -lady.width / 2,
    -lady.height / 2,
    lady.width,
    lady.height
  );
  
  ctx.restore();
}

function update() {
  switch (lady.state) {
    case "walking":
      lady.x += lady.speed;
      if (lady.x >= 680) { // Reached house - start turning to face it
        lady.state = "turning";
      }
      break;

    case "turning":
      lady.turnFrames++;
      if (lady.turnFrames >= lady.maxTurnFrames) {
        lady.direction = -1; // Face left/back (toward house)
        lady.state = "stopped";

        // Play door open sound after turning is complete (only once)
        if (!lady.doorSoundPlayed) {
          lady.doorSoundPlayed = true;
          console.log('Character finished turning, playing door open sound...');
          if (doorOpenSound) {
            // Add a small delay before playing the door sound
            setTimeout(() => {
              doorOpenSound.currentTime = 0; // Reset sound to beginning
              doorOpenSound.play().catch(error => {
                console.log('Could not play door open sound:', error);
              });
            }, 300); // 300ms delay after turning before door sound
          } else {
            console.log('Door open sound not loaded yet');
          }
        }

        // Longer delay to show her facing the house and let door sound play, then redirect
        setTimeout(() => {
          showLoadingAndRedirect();
        }, 1200); // 1.2 second total delay (300ms + door sound + buffer)
      }
      break;
  }
}

function gameLoop() {
  ctx.clearRect(0, 0, canvas.width, canvas.height);
  ctx.drawImage(bgImage, 0, 0, canvas.width, canvas.height);
  drawLady();
  update();
  requestAnimationFrame(gameLoop);
}

// Wait for all images to load
let imagesLoaded = 0;
function checkStart() {
  imagesLoaded++;
  if (imagesLoaded === 3) { // Now waiting for 3 images
    // Set canvas size to match background
    canvas.width = bgImage.width;
    canvas.height = bgImage.height;
    gameLoop();
  }
}

// Set up image load handlers
bgImage.onload = checkStart;
ladyFrontImage.onload = checkStart;
ladyBackImage.onload = checkStart;

// Loading screen and redirect function
let hasRedirected = false; // Prevent multiple redirects

function showLoadingAndRedirect() {
  if (hasRedirected) return; // Prevent multiple calls
  hasRedirected = true;

  // Create loading screen overlay
  const loadingOverlay = document.createElement('div');
  loadingOverlay.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    font-family: 'Press Start 2P', cursive;
    color: white;
    font-size: 24px;
  `;
  loadingOverlay.textContent = 'Loading...';
  document.body.appendChild(loadingOverlay);

  // Redirect after a short delay
  setTimeout(() => {
    window.location.href = './enter house/enter.html';
  }, 1500); // 1.5 second loading screen
}