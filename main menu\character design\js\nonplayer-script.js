// Sound manager for menu clicks
let menuClickSound;
const menuSoundPaths = [
    "../../Sound effects/Click menu and settings .mp3",
    "../Sound effects/Click menu and settings .mp3",
    "./Sound effects/Click menu and settings .mp3",
    "../../Sound effects/click-menu.mp3",
    "../Sound effects/click-menu.mp3",
    "./Sound effects/click-menu.mp3"
];

// Load menu click sound
function loadMenuClickSound() {
    let pathIndex = 0;

    function tryNextPath() {
        if (pathIndex >= menuSoundPaths.length) {
            console.error('Could not load menu click sound from any path');
            return;
        }

        menuClickSound = new Audio(menuSoundPaths[pathIndex]);
        menuClickSound.preload = "auto";
        menuClickSound.volume = 0.7;

        menuClickSound.addEventListener('canplaythrough', () => {
            console.log(`Menu click sound loaded successfully from: ${menuSoundPaths[pathIndex]}`);
        });

        menuClickSound.addEventListener('error', (e) => {
            console.log(`Failed to load menu sound from path ${pathIndex}: ${menuSoundPaths[pathIndex]}`);
            pathIndex++;
            tryNextPath();
        });
    }

    tryNextPath();
}

// Play menu click sound
function playMenuClickSound() {
    if (menuClickSound) {
        menuClickSound.currentTime = 0;
        menuClickSound.play().catch(error => {
            console.log('Could not play menu click sound:', error);
        });
    }
}

function selectCharacter(imageFile) {
  playMenuClickSound(); // Play click sound when selecting character

  // Store selected image
  localStorage.setItem('selectedCharacter', imageFile);

  // Create and show popup
  const popup = document.createElement('div');
  popup.className = 'popup';
  popup.textContent = 'Character selected!';
  document.body.appendChild(popup);

  // Show popup
  popup.style.display = 'block';

  // Hide popup and redirect after delay
  setTimeout(function() {
    popup.style.display = 'none';
    window.location.href = 'mainPlayer.html';
  }, 1500);
}

// Test function to clear localStorage (for debugging)
function clearStorage() {
  localStorage.clear();
  location.reload();
}

// Hide saved character from selection when page loads
document.addEventListener('DOMContentLoaded', function() {
  // Load menu click sound
  loadMenuClickSound();

  const savedCharacter = localStorage.getItem('savedCharacter');

  // Determine which character is currently saved (either from localStorage or default Frame 11)
  let currentSavedCharacter;
  if (savedCharacter) {
    currentSavedCharacter = savedCharacter.split('/').pop();
  } else {
    // If no saved character, Frame 11 is the default saved character
    currentSavedCharacter = 'Frame 11.png';
  }

  // Hide the card that matches the current saved character
  const cards = document.querySelectorAll('.card');
  cards.forEach(card => {
    const characterPath = card.getAttribute('data-character');
    if (characterPath) {
      const cardCharacterFile = characterPath.split('/').pop();
      if (cardCharacterFile === currentSavedCharacter) {
        card.classList.add('hidden');
      }
    }
  });

  // Force hide Frame 11 if no saved character (ensure default behavior)
  if (!savedCharacter) {
    const frame11Card = document.querySelector('[data-character="img/Frame 11.png"]');
    if (frame11Card) {
      frame11Card.classList.add('hidden');
    }
  }

  // Add click sound to back button
  const backButton = document.querySelector('.back-button');
  if (backButton) {
    backButton.addEventListener('click', function() {
      playMenuClickSound();
    });
  }
});






