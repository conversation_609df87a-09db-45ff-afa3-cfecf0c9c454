<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="message.css">
    <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap" rel="stylesheet">
    <title>Game Character with Numbered Background</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: 'Press Start 2P', cursive, sans-serif;
        }
        
        #game-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            overflow: hidden;
        }
        
        #background {
            position: absolute;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: 0;
        }
        
        #ground-overlay {
            display: none; /* Hide instead of removing to preserve structure */
        }
        
        /* Character Container - Made larger */
        #character-container {
            position: absolute;
            bottom: -100px; /* Lower position (was 100px) */
            left: -100px;
            width: 600px; /* Increased from 500px */
            height: 650px; /* Increased from 550px */
            transition: left 4s cubic-bezier(0.25, 0.1, 0.25, 1);
            z-index: 2;
        }
        
        /* Character Image */
        #character {
            position: relative;
            width: 100%;
            height: 100%;
            background-image: url('npcs\ chatacter\ design\ 1.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center bottom;
            animation: walkBounce 0.8s infinite;
            transform-origin: bottom center;
            left: 50%;
        }
        
        /* Pointing Arm - Adjusted for larger character */
        #pointing-arm {
            position: absolute;
            width: 50px; /* Increased from 40px */
            height: 100px; /* Increased from 80px */
            /* background-color: #5D4037; */
            border-radius: 25px;
            right: 30px; /* Adjusted position */
            top: 100px; /* Adjusted position */
            transform-origin: top center;
            display: none;
            z-index: -1;
        }
        
        /* Improved Message Box - Adjusted position */
        #message-box {
            position: absolute;
            bottom: 400px; /* Decreased from 450px to move lower */
            left: 35%; /* Decreased from 42% to move more to the left */
            transform: translateX(-50%) scale(0);
            width: 350px;
            padding: 20px;
            background-color: white;
            border-radius: 15px;
            border: 3px solid black;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
            opacity: 0;
            transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            text-align: center;
            font-size: 18px;
            line-height: 1.6;
            color: black;
            z-index: 3;
            font-family: 'Press Start 2P', cursive, sans-serif;
        }
        
        #message-box::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 100%;
            margin-left: -50px;
            border-width: 20px 20px 0;
            border-style: solid;
            border-color: white transparent;
        }
        
        /* Continue Button - Improved styling */
        #continue-btn {
            display: none; /* This will hide it if it still exists in the DOM */
        }
        
        /* Add fade-in animation for the Next button */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* Typewriter cursor effect */
        .typing::after {
            content: '|';
            animation: blink 1s infinite;
            color: #333;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
        


    </style>
</head>
<body>
    <div id="game-container">
        <!-- Background image -->
        <img id="background" src="background.png" alt="Numbered background">
        
        <!-- Ground overlay for better visibility -->
        <div id="ground-overlay"></div>
        
        <!-- Character with Image -->
        <div id="character-container">
            <div id="character">
                <div id="pointing-arm"></div>
            </div>
        </div>
        
        <div id="message-box">
        </div>
        
        <!-- Removed the continue button -->
    </div>


    <script>
    document.addEventListener('DOMContentLoaded', function () {
        const container = document.getElementById('character-container');
        const character = document.getElementById('character');
        const pointingArm = document.getElementById('pointing-arm');
        const messageBox = document.getElementById('message-box');
        // Removed reference to continue button since it no longer exists

        // New: message array
        const messages = [
            "Ah, traveler! Thank the spirits you've arrived. Our village is in dire trouble. The Sinmo has unleashed chaos upon us, destroying homes and leaving us in despair!",
            "We need your help to save our home. To restore what has been lost, we must rebuild the Chorten—the heart of our village. But for that, we need five treasures, each guarded by perilous quests.",
            "Each treasure holds a piece of the power we need. Complete these quests, and we can gather the treasures to restore the Chorten.",
            "Will you help us?"
        ];

        let messageIndex = 0;

        // TTS variables
        let speechSynthesis = window.speechSynthesis;
        let currentUtterance = null;
        let isTyping = false;
        let typewriterInterval = null;
        let audioEnabled = false;
        let voicesLoaded = false;

        // Audio notification
        let messageSound = null;

        // Video notification for first message
        let firstMessageVideo = null;

        // Video notification for second message
        let secondMessageVideo = null;

        // Video notification for third message
        let thirdMessageVideo = null;

        // Video notification for fourth message
        let fourthMessageVideo = null;

        // Load message notification sound
        function loadMessageSound() {
            messageSound = new Audio('message sound.mp3');
            messageSound.volume = 0.7;
            messageSound.preload = 'auto';

            messageSound.addEventListener('canplaythrough', () => {
                console.log('Message sound loaded successfully');
            });

            messageSound.addEventListener('error', (e) => {
                console.log('Failed to load message sound:', e);
            });
        }

        // Load first message video
        function loadFirstMessageVideo() {
            firstMessageVideo = document.createElement('video');
            firstMessageVideo.src = '1.mp4';
            firstMessageVideo.volume = 0.7;
            firstMessageVideo.preload = 'auto';
            firstMessageVideo.style.display = 'none'; // Hidden video element

            firstMessageVideo.addEventListener('canplaythrough', () => {
                console.log('First message video loaded successfully');
            });

            firstMessageVideo.addEventListener('error', (e) => {
                console.log('Failed to load first message video:', e);
            });

            // Add to document
            document.body.appendChild(firstMessageVideo);
        }

        // Load second message video
        function loadSecondMessageVideo() {
            secondMessageVideo = document.createElement('video');
            secondMessageVideo.src = '2.mp4';
            secondMessageVideo.volume = 0.7;
            secondMessageVideo.preload = 'auto';
            secondMessageVideo.style.display = 'none'; // Hidden video element

            secondMessageVideo.addEventListener('canplaythrough', () => {
                console.log('Second message video loaded successfully');
            });

            secondMessageVideo.addEventListener('error', (e) => {
                console.log('Failed to load second message video:', e);
            });

            // Add to document
            document.body.appendChild(secondMessageVideo);
        }

        // Load third message video
        function loadThirdMessageVideo() {
            thirdMessageVideo = document.createElement('video');
            thirdMessageVideo.src = '3.mp4';
            thirdMessageVideo.volume = 0.7;
            thirdMessageVideo.preload = 'auto';
            thirdMessageVideo.style.display = 'none'; // Hidden video element

            thirdMessageVideo.addEventListener('canplaythrough', () => {
                console.log('Third message video loaded successfully');
            });

            thirdMessageVideo.addEventListener('error', (e) => {
                console.log('Failed to load third message video:', e);
            });

            // Add to document
            document.body.appendChild(thirdMessageVideo);
        }

        // Load fourth message video
        function loadFourthMessageVideo() {
            fourthMessageVideo = document.createElement('video');
            fourthMessageVideo.src = '4.mp4';
            fourthMessageVideo.volume = 0.7;
            fourthMessageVideo.preload = 'auto';
            fourthMessageVideo.style.display = 'none'; // Hidden video element

            fourthMessageVideo.addEventListener('canplaythrough', () => {
                console.log('Fourth message video loaded successfully');
            });

            fourthMessageVideo.addEventListener('error', (e) => {
                console.log('Failed to load fourth message video:', e);
            });

            // Add to document
            document.body.appendChild(fourthMessageVideo);
        }

        // Load voices and enable audio
        function initializeAudio() {
            audioEnabled = true;

            // Load message sound
            loadMessageSound();

            // Load first message video
            loadFirstMessageVideo();

            // Load second message video
            loadSecondMessageVideo();

            // Load third message video
            loadThirdMessageVideo();

            // Load fourth message video
            loadFourthMessageVideo();

            // Check TTS support
            console.log('Speech synthesis supported:', !!speechSynthesis);
            console.log('Initial voices count:', speechSynthesis.getVoices().length);

            // Load voices if not already loaded
            if (speechSynthesis.getVoices().length === 0) {
                console.log('Waiting for voices to load...');
                speechSynthesis.addEventListener('voiceschanged', () => {
                    voicesLoaded = true;
                    const voices = speechSynthesis.getVoices();
                    console.log('Voices loaded:', voices.length);
                    voices.forEach((voice, index) => {
                        console.log(`Voice ${index}: ${voice.name} (${voice.lang})`);
                    });


                }, { once: true });

                // Force voice loading
                speechSynthesis.getVoices();
            } else {
                voicesLoaded = true;
                const voices = speechSynthesis.getVoices();
                console.log('Voices already available:', voices.length);
                voices.forEach((voice, index) => {
                    console.log(`Voice ${index}: ${voice.name} (${voice.lang})`);
                });


            }

            console.log('Audio context enabled');
        }



        // Try to enable audio immediately on page load
        function tryAutoEnableAudio() {
            audioEnabled = true;
            loadMessageSound();
            loadFirstMessageVideo();
            loadSecondMessageVideo();
            loadThirdMessageVideo();
            loadFourthMessageVideo();

            // Try to prime the audio context
            if (messageSound) {
                messageSound.play().then(() => {
                    messageSound.pause();
                    messageSound.currentTime = 0;
                    console.log('Audio context primed successfully');
                }).catch(() => {
                    console.log('Audio context priming failed - will need user interaction');
                });
            }

            if (speechSynthesis.getVoices().length > 0) {
                voicesLoaded = true;
                console.log('Voices available on load:', speechSynthesis.getVoices().length);
            } else {
                speechSynthesis.addEventListener('voiceschanged', () => {
                    voicesLoaded = true;
                    console.log('Voices loaded after change:', speechSynthesis.getVoices().length);
                }, { once: true });
            }

            // Try to prime TTS
            if (speechSynthesis) {
                const testUtterance = new SpeechSynthesisUtterance('');
                testUtterance.volume = 0;
                speechSynthesis.speak(testUtterance);
                speechSynthesis.cancel();
                console.log('TTS context primed');
            }
        }

        // Enable audio context on first user interaction (fallback)
        document.addEventListener('click', initializeAudio, { once: true });

        // Try to enable audio immediately
        window.addEventListener('load', tryAutoEnableAudio);

        // Also try immediately when DOM is ready
        tryAutoEnableAudio();

        // Try to trigger audio context on any page interaction
        document.addEventListener('mousemove', function enableOnMove() {
            if (!audioEnabled) {
                tryAutoEnableAudio();
            }
            document.removeEventListener('mousemove', enableOnMove);
        }, { once: true });

        // Function to speak text using TTS
        function speakText(text, onStart, onEnd) {
            console.log('speakText called with:', text.substring(0, 50) + '...');
            console.log('audioEnabled:', audioEnabled);
            console.log('speechSynthesis available:', !!speechSynthesis);

            // Try to enable audio if not already enabled
            if (!audioEnabled) {
                console.log('Attempting to auto-enable audio...');
                audioEnabled = true;
                loadMessageSound();
                loadFirstMessageVideo();
                loadSecondMessageVideo();
                loadThirdMessageVideo();
                loadFourthMessageVideo();
            }

            // Play notification sound first (try even without user interaction)
            if (messageSound) {
                messageSound.play().then(() => {
                    console.log('Message notification sound played automatically');
                }).catch(e => {
                    console.log('Failed to play notification sound automatically:', e);
                });
            }

            if (!speechSynthesis) {
                console.log('Speech synthesis not supported');
                if (onEnd) onEnd();
                return;
            }

            // Wait for voices to load if needed
            const startSpeech = () => {
                console.log('Starting speech synthesis...');

                // Stop any current speech
                speechSynthesis.cancel();

                currentUtterance = new SpeechSynthesisUtterance(text);
                currentUtterance.rate = 0.8; // Slightly slower for better sync
                currentUtterance.pitch = 1.0;
                currentUtterance.volume = 1.0; // Max volume

                // Try to use a more character-appropriate voice
                const voices = speechSynthesis.getVoices();
                console.log('Available voices for speech:', voices.length);

                if (voices.length > 0) {
                    const preferredVoice = voices.find(voice =>
                        voice.lang.includes('en') &&
                        (voice.name.includes('Google') ||
                         voice.name.includes('Microsoft') ||
                         voice.name.includes('Natural') ||
                         voice.name.includes('Female'))
                    ) || voices.find(voice => voice.lang.includes('en')) || voices[0];

                    if (preferredVoice) {
                        currentUtterance.voice = preferredVoice;
                        console.log('Using voice:', preferredVoice.name, preferredVoice.lang);
                    } else {
                        console.log('No preferred voice found, using default');
                    }
                } else {
                    console.log('No voices available');
                }

                currentUtterance.onstart = () => {
                    console.log('TTS speech started successfully');
                    if (onStart) onStart();
                };

                currentUtterance.onend = () => {
                    console.log('TTS speech ended');
                    if (onEnd) onEnd();
                };

                currentUtterance.onerror = (event) => {
                    console.error('TTS error:', event.error);
                    console.error('TTS error details:', event);
                    if (onEnd) onEnd();
                };

                console.log('Calling speechSynthesis.speak()...');
                speechSynthesis.speak(currentUtterance);

                // Check if speech is actually speaking
                setTimeout(() => {
                    console.log('Speech synthesis speaking:', speechSynthesis.speaking);
                    console.log('Speech synthesis pending:', speechSynthesis.pending);
                }, 100);
            };

            // Start speech immediately if voices are loaded, otherwise wait a bit
            if (voicesLoaded || speechSynthesis.getVoices().length > 0) {
                console.log('Voices ready, starting speech immediately');
                startSpeech();
            } else {
                console.log('Waiting for voices to load...');
                setTimeout(() => {
                    console.log('Timeout reached, attempting speech anyway');
                    startSpeech();
                }, 500);
            }
        }

        // Typewriter effect with TTS synchronization
        function typeMessageWithVoice(message, callback) {
            console.log('typeMessageWithVoice called for message:', message.substring(0, 50) + '...');

            isTyping = true;
            messageBox.innerHTML = '';
            messageBox.classList.add('typing');
            let charIndex = 0;

            // Start typewriter effect immediately, regardless of TTS
            console.log('Starting typewriter effect immediately');
            const messageLength = message.length;
            const baseCharDelay = 50; // Base typing speed in milliseconds

            // Start typewriter effect
            typewriterInterval = setInterval(() => {
                if (charIndex < message.length) {
                    messageBox.innerHTML = message.substring(0, charIndex + 1);
                    charIndex++;
                } else {
                    clearInterval(typewriterInterval);
                    messageBox.classList.remove('typing');
                    isTyping = false;
                    console.log('Typewriter effect completed');
                    if (callback) callback();
                }
            }, baseCharDelay);

            // Try to start TTS in parallel (may or may not work due to browser restrictions)
            speakText(message,
                () => {
                    console.log('TTS started successfully');
                },
                () => {
                    console.log('TTS ended');
                }
            );
        }

        // Show message with appropriate effect based on message index
        function typeMessage(message, callback) {
            // Always display messages instantly (no typewriter effect)
            messageBox.innerHTML = message;

            // Play video only for the first message (other videos play on button press)
            if (messageIndex === 0 && firstMessageVideo) {
                // Stop all videos first, then play the first message video
                stopAllVideos();
                console.log('Playing 1.mp4 for first message');
                firstMessageVideo.currentTime = 0;
                firstMessageVideo.play().then(() => {
                    console.log('First message video played successfully');
                }).catch(error => {
                    console.log('Could not play first message video:', error);
                });
            }

            if (callback) callback();
        }

        // Function to complete typing instantly
        function completeTyping() {
            if (isTyping && typewriterInterval) {
                clearInterval(typewriterInterval);
                speechSynthesis.cancel();
                messageBox.innerHTML = messages[messageIndex];
                messageBox.classList.remove('typing');
                isTyping = false;
            }
        }

        // Create a next button dynamically with improved styling
        const nextBtn = document.createElement('button');
        nextBtn.innerText = 'Next';
        nextBtn.style.position = 'absolute';
        nextBtn.style.bottom = '350px';
        nextBtn.style.left = '35%';
        nextBtn.style.transform = 'translateX(-50%)';
        nextBtn.style.padding = '10px 20px';
        nextBtn.style.background = '#4CAF50';
        nextBtn.style.color = 'white';
        nextBtn.style.border = 'none';
        nextBtn.style.borderRadius = '20px';
        nextBtn.style.cursor = 'pointer';
        nextBtn.style.opacity = '0';
        nextBtn.style.display = 'none';
        nextBtn.style.zIndex = '4';
        nextBtn.style.fontFamily = "'Press Start 2P', cursive, sans-serif";
        nextBtn.style.fontSize = '14px';
        nextBtn.style.boxShadow = '0 4px 8px rgba(0,0,0,0.3)';
        nextBtn.style.transition = 'opacity 0.5s ease';
        nextBtn.title = 'Click to continue'; // Tooltip
        document.getElementById('game-container').appendChild(nextBtn);

        // Audio controls removed - automatic voiceover only

        // Removed back button code

        // Animate character popping up from center
        container.style.left = '50%';
        container.style.transform = 'translateX(-50%) translateY(100%) scale(0.5)';
        container.style.opacity = '0';
        container.style.transition = 'all 1s ease-out';

        setTimeout(() => {
            container.style.transform = 'translateX(-50%) translateY(0) scale(1)';
            container.style.opacity = '1';

            setTimeout(() => {
                pointingArm.style.display = 'block';
                pointingArm.style.animation = 'pointArm 1s forwards';

                setTimeout(() => {
                    messageBox.style.opacity = '1';
                    messageBox.style.transform = 'translateX(-50%) scale(1)';

                    // Show next button immediately when message shows
                    nextBtn.style.display = 'block';
                    setTimeout(() => {
                        nextBtn.style.opacity = '1';
                    }, 50);

                    // Show the first message instantly
                    // Add small delay to ensure message box is fully visible
                    setTimeout(() => {
                        console.log('Starting first message instantly');
                        typeMessage(messages[messageIndex]);
                    }, 200);

                    // Audio controls removed - automatic voiceover only
                }, 1000);
            }, 500);
        }, 500);

        // Function to stop all currently playing videos
        function stopAllVideos() {
            if (firstMessageVideo && !firstMessageVideo.paused) {
                firstMessageVideo.pause();
                firstMessageVideo.currentTime = 0;
                console.log('Stopped first message video');
            }
            if (secondMessageVideo && !secondMessageVideo.paused) {
                secondMessageVideo.pause();
                secondMessageVideo.currentTime = 0;
                console.log('Stopped second message video');
            }
            if (thirdMessageVideo && !thirdMessageVideo.paused) {
                thirdMessageVideo.pause();
                thirdMessageVideo.currentTime = 0;
                console.log('Stopped third message video');
            }
            if (fourthMessageVideo && !fourthMessageVideo.paused) {
                fourthMessageVideo.pause();
                fourthMessageVideo.currentTime = 0;
                console.log('Stopped fourth message video');
            }
        }

        // On next message click
        nextBtn.addEventListener('click', () => {
            // Stop all currently playing videos first
            stopAllVideos();

            // Play video immediately when Next button is pressed based on current message
            if (messageIndex === 0 && secondMessageVideo) {
                console.log('Playing 2.mp4 immediately on Next button press');
                secondMessageVideo.currentTime = 0;
                secondMessageVideo.play().then(() => {
                    console.log('Second message video played successfully on button press');
                }).catch(error => {
                    console.log('Could not play second message video on button press:', error);
                });
            } else if (messageIndex === 1 && thirdMessageVideo) {
                console.log('Playing 3.mp4 immediately on Next button press');
                thirdMessageVideo.currentTime = 0;
                thirdMessageVideo.play().then(() => {
                    console.log('Third message video played successfully on button press');
                }).catch(error => {
                    console.log('Could not play third message video on button press:', error);
                });
            } else if (messageIndex === 2 && fourthMessageVideo) {
                console.log('Playing 4.mp4 immediately on Next button press');
                fourthMessageVideo.currentTime = 0;
                fourthMessageVideo.play().then(() => {
                    console.log('Fourth message video played successfully on button press');
                }).catch(error => {
                    console.log('Could not play fourth message video on button press:', error);
                });
            }

            // Advance to next message
            messageIndex++;
            if (messageIndex < messages.length) {
                // Check if this is the last message
                if (messageIndex === messages.length - 1) {
                    nextBtn.style.display = 'none';

                    // Show the last message
                    typeMessage(messages[messageIndex]);

                    // Show Yes/No buttons immediately when last message shows
                    // Create Yes/No buttons directly below the message
                    const choiceContainer = document.createElement('div');
                    choiceContainer.className = 'choice-container';
                    choiceContainer.style.position = 'absolute';
                    choiceContainer.style.bottom = '300px';
                    choiceContainer.style.left = '35%';
                    choiceContainer.style.transform = 'translateX(-50%)';
                    choiceContainer.style.display = 'flex';
                    choiceContainer.style.gap = '20px';
                    choiceContainer.style.zIndex = '3';
                    document.getElementById('game-container').appendChild(choiceContainer);

                    // Yes button
                    const yesButton = document.createElement('button');
                    yesButton.textContent = "Yes";
                    yesButton.style.padding = '12px 30px';
                    yesButton.style.background = 'linear-gradient(to bottom, #4CAF50 0%, #388E3C 100%)';
                    yesButton.style.color = 'white';
                    yesButton.style.border = 'none';
                    yesButton.style.borderRadius = '30px';
                    yesButton.style.cursor = 'pointer';
                    yesButton.style.fontSize = '16px';
                    yesButton.style.fontFamily = "'Press Start 2P', cursive, sans-serif";
                    yesButton.style.boxShadow = '0 4px 8px rgba(0,0,0,0.3)';
                    yesButton.style.transition = 'all 0.3s ease';

                    // No button
                    const noButton = document.createElement('button');
                    noButton.textContent = "No";
                    noButton.style.padding = '12px 30px';
                    noButton.style.background = 'linear-gradient(to bottom, #f44336 0%, #d32f2f 100%)';
                    noButton.style.color = 'white';
                    noButton.style.border = 'none';
                    noButton.style.borderRadius = '30px';
                    noButton.style.cursor = 'pointer';
                    noButton.style.fontSize = '16px';
                    noButton.style.fontFamily = "'Press Start 2P', cursive, sans-serif";
                    noButton.style.boxShadow = '0 4px 8px rgba(0,0,0,0.3)';
                    noButton.style.transition = 'all 0.3s ease';

                    // Add hover effects
                    yesButton.addEventListener('mouseover', () => {
                        yesButton.style.transform = 'translateY(-2px)';
                        yesButton.style.boxShadow = '0 6px 12px rgba(0,0,0,0.3)';
                    });

                    yesButton.addEventListener('mouseout', () => {
                        yesButton.style.transform = 'translateY(0)';
                        yesButton.style.boxShadow = '0 4px 8px rgba(0,0,0,0.3)';
                    });

                    noButton.addEventListener('mouseover', () => {
                        noButton.style.transform = 'translateY(-2px)';
                        noButton.style.boxShadow = '0 6px 12px rgba(0,0,0,0.3)';
                    });

                    noButton.addEventListener('mouseout', () => {
                        noButton.style.transform = 'translateY(0)';
                        noButton.style.boxShadow = '0 4px 8px rgba(0,0,0,0.3)';
                    });

                    // Add click events
                    yesButton.addEventListener('click', () => {
                        // Stop all videos first
                        stopAllVideos();

                        // Hide the message and choice container completely
                        messageBox.style.display = 'none';
                        choiceContainer.style.display = 'none';

                        // Hide all game elements first
                        const characterContainer = document.getElementById('character-container');
                        const background = document.getElementById('background');

                        if (characterContainer) characterContainer.style.display = 'none';

                        // Change background to bg pic-01.png
                        if (background) {
                            background.src = 'bg pic-01.png';
                        }

                        // Wait for background to change, then show loading screen
                        setTimeout(() => {
                            // Create loading overlay (transparent background since bg is already changed)
                            const loadingOverlay = document.createElement('div');
                            loadingOverlay.style.position = 'fixed';
                            loadingOverlay.style.top = '0';
                            loadingOverlay.style.left = '0';
                            loadingOverlay.style.width = '100%';
                            loadingOverlay.style.height = '100%';
                            loadingOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.3)'; // Semi-transparent overlay
                            loadingOverlay.style.display = 'flex';
                            loadingOverlay.style.flexDirection = 'column';
                            loadingOverlay.style.justifyContent = 'center';
                            loadingOverlay.style.alignItems = 'center';
                            loadingOverlay.style.zIndex = '9999';
                            loadingOverlay.style.color = 'white';
                            loadingOverlay.style.fontFamily = "'Press Start 2P', cursive, sans-serif";

                            // Create loading content
                            loadingOverlay.innerHTML = `
                                <div style="text-align: center; background: rgba(0, 0, 0, 0.3); padding: 40px; border-radius: 10px; border: 3px solid #fff;">
                                    <div style="font-size: 24px; margin-bottom: 30px; text-shadow: 2px 2px 4px rgba(0,0,0,0.8);">
                                        Loading FlappyDaka Game
                                    </div>
                                    <div class="loading-spinner" style="
                                        width: 60px;
                                        height: 60px;
                                        border: 6px solid rgba(255,255,255,0.3);
                                        border-top: 6px solid white;
                                        border-radius: 50%;
                                        animation: spin 1s linear infinite;
                                        margin: 0 auto 20px auto;
                                    "></div>
                                    <div style="font-size: 14px; opacity: 0.8;">
                                        Preparing your adventure...
                                    </div>
                                </div>
                            `;

                            // Add spinner animation
                            const style = document.createElement('style');
                            style.textContent = `
                                @keyframes spin {
                                    0% { transform: rotate(0deg); }
                                    100% { transform: rotate(360deg); }
                                }
                            `;
                            document.head.appendChild(style);

                            // Add loading overlay to page
                            document.body.appendChild(loadingOverlay);

                            // Redirect after 3 seconds
                            setTimeout(() => {
                                window.location.href = 'FlappyDaka Game-main/index.html';
                            }, 3000);
                        }, 300); // Short delay to let background change
                    });
                    
                    noButton.addEventListener('click', () => {
                        // Stop all videos first
                        stopAllVideos();

                        // Hide choice buttons and show goodbye message
                        choiceContainer.style.display = 'none';
                        typeMessage("I understand. Farewell, traveler...", () => {
                            // Redirect back to chapter selection after message shows
                            setTimeout(() => {
                                window.location.href = "../chapter.html";
                            }, 2000);
                        });
                    });

                    // Add buttons to container
                    choiceContainer.appendChild(yesButton);
                    choiceContainer.appendChild(noButton);
                } else {
                    // For non-last messages, show instantly (no voiceover)
                    typeMessage(messages[messageIndex]);
                }
            }
        });

        // Removed back button event listener

        // Adjust background image to fit
        function resizeBackground() {
            const bg = document.getElementById('background');
            if (bg.complete) {
                bg.style.width = '100%';
                bg.style.height = '100%';
                bg.style.objectFit = 'cover';
            }
        }

        window.addEventListener('load', resizeBackground);
        window.addEventListener('resize', resizeBackground);
        document.getElementById('background').addEventListener('load', resizeBackground);

        // Initial resize call
        resizeBackground();

        // Stop all videos when page is about to unload
        window.addEventListener('beforeunload', () => {
            stopAllVideos();
        });

        // Stop all videos when page loses focus
        window.addEventListener('blur', () => {
            stopAllVideos();
        });
    });
    
</script>

</body>
</html>
