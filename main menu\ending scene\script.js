// Sound manager for ending music and menu clicks
let endingSound;
let menuClickSound;

const endingSoundPaths = [
    "../../Sound effects/The end .mp3",
    "../Sound effects/The end .mp3",
    "./Sound effects/The end .mp3",
    "Sound effects/The end .mp3"
];

const menuSoundPaths = [
    "../../Sound effects/Click menu and settings .mp3",
    "../Sound effects/Click menu and settings .mp3",
    "./Sound effects/Click menu and settings .mp3",
    "../../Sound effects/click-menu.mp3",
    "../Sound effects/click-menu.mp3",
    "./Sound effects/click-menu.mp3"
];

// Load ending sound
function loadEndingSound() {
    let pathIndex = 0;

    function tryNextPath() {
        if (pathIndex >= endingSoundPaths.length) {
            console.error('Could not load ending sound from any path');
            return;
        }

        endingSound = new Audio(endingSoundPaths[pathIndex]);
        endingSound.preload = "auto";
        endingSound.volume = 0.8;
        endingSound.loop = false; // Play once

        endingSound.addEventListener('canplaythrough', () => {
            console.log(`Ending sound loaded successfully from: ${endingSoundPaths[pathIndex]}`);
        });

        endingSound.addEventListener('error', (e) => {
            console.log(`Failed to load ending sound from path ${pathIndex}: ${endingSoundPaths[pathIndex]}`);
            pathIndex++;
            tryNextPath();
        });
    }

    tryNextPath();
}

// Load menu click sound
function loadMenuClickSound() {
    let pathIndex = 0;

    function tryNextPath() {
        if (pathIndex >= menuSoundPaths.length) {
            console.error('Could not load menu click sound from any path');
            return;
        }

        menuClickSound = new Audio(menuSoundPaths[pathIndex]);
        menuClickSound.preload = "auto";
        menuClickSound.volume = 0.7;

        menuClickSound.addEventListener('canplaythrough', () => {
            console.log(`Menu click sound loaded successfully from: ${menuSoundPaths[pathIndex]}`);
        });

        menuClickSound.addEventListener('error', (e) => {
            console.log(`Failed to load menu sound from path ${pathIndex}: ${menuSoundPaths[pathIndex]}`);
            pathIndex++;
            tryNextPath();
        });
    }

    tryNextPath();
}

// Play ending sound
function playEndingSound() {
    if (endingSound) {
        endingSound.currentTime = 0;
        endingSound.play().then(() => {
            console.log('Ending sound played successfully');
        }).catch(error => {
            console.log('Could not play ending sound:', error);
        });
    }
}

// Play menu click sound
function playMenuClickSound() {
    if (menuClickSound) {
        menuClickSound.currentTime = 0;
        menuClickSound.play().catch(error => {
            console.log('Could not play menu click sound:', error);
        });
    }
}

window.onload = () => {
  // Load ending sound and menu click sound
  loadEndingSound();
  loadMenuClickSound();

  // Enable audio context on first user interaction (for browser autoplay policies)
  document.addEventListener('click', function enableAudio() {
    if (endingSound) {
      // Try to play and immediately pause to enable audio context
      endingSound.play().then(() => {
        endingSound.pause();
        endingSound.currentTime = 0;
      }).catch(() => {
        // Ignore errors during audio context initialization
      });
    }
    if (menuClickSound) {
      // Also enable menu click sound
      menuClickSound.play().then(() => {
        menuClickSound.pause();
        menuClickSound.currentTime = 0;
      }).catch(() => {
        // Ignore errors during audio context initialization
      });
    }
    // Remove this listener after first interaction
    document.removeEventListener('click', enableAudio);
  }, { once: true });

  // Handle black screen fade-out on page load
  const blackScreen = document.getElementById('black-screen-overlay');
  if (blackScreen) {
    setTimeout(() => {
      blackScreen.style.opacity = '0';
      // Remove the overlay after fade completes
      setTimeout(() => {
        blackScreen.remove();
      }, 2000);
    }, 500);
  }

  const girlBack = document.querySelector('.girl-back');
  const girlSide = document.querySelector('.girl-side');
  const endMessage = document.querySelector('.end-message');

  // Animate the girl being pulled up to the moon
  girlBack.style.transform = 'translateY(-400px)';

  // Animate the side-view girl walking to the house
  girlSide.style.left = '70%';

  // Play ending sound after a short delay (when animations start)
  setTimeout(() => {
    playEndingSound();
  }, 1000);

  // After animations, fade in the end message
  setTimeout(() => {
    endMessage.classList.add('show');
  }, 4000); // Matches character animation duration

  // Add click sound to back button
  const backButton = document.querySelector('.back-button');
  if (backButton) {
    backButton.addEventListener('click', function() {
      playMenuClickSound();
    });
  }
};

