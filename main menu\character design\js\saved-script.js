// Sound manager for menu clicks
let menuClickSound;
const menuSoundPaths = [
    "../../Sound effects/Click menu and settings .mp3",
    "../Sound effects/Click menu and settings .mp3",
    "./Sound effects/Click menu and settings .mp3",
    "../../Sound effects/click-menu.mp3",
    "../Sound effects/click-menu.mp3",
    "./Sound effects/click-menu.mp3"
];

// Load menu click sound
function loadMenuClickSound() {
    let pathIndex = 0;

    function tryNextPath() {
        if (pathIndex >= menuSoundPaths.length) {
            console.error('Could not load menu click sound from any path');
            return;
        }

        menuClickSound = new Audio(menuSoundPaths[pathIndex]);
        menuClickSound.preload = "auto";
        menuClickSound.volume = 0.7;

        menuClickSound.addEventListener('canplaythrough', () => {
            console.log(`Menu click sound loaded successfully from: ${menuSoundPaths[pathIndex]}`);
        });

        menuClickSound.addEventListener('error', (e) => {
            console.log(`Failed to load menu sound from path ${pathIndex}: ${menuSoundPaths[pathIndex]}`);
            pathIndex++;
            tryNextPath();
        });
    }

    tryNextPath();
}

// Play menu click sound
function playMenuClickSound() {
    if (menuClickSound) {
        menuClickSound.currentTime = 0;
        menuClickSound.play().catch(error => {
            console.log('Could not play menu click sound:', error);
        });
    }
}

document.addEventListener('DOMContentLoaded', function() {
  // Load menu click sound
  loadMenuClickSound();

  const savedCharacter = localStorage.getItem('savedCharacter');
  if (savedCharacter) {
    document.getElementById('savedCharacter').src = savedCharacter;
  } else {
    // If no saved character, show Frame 11 as default
    document.getElementById('savedCharacter').src = 'img/Frame 11.png';
  }

  // Add click sound to back button
  const backButton = document.querySelector('.back-button');
  if (backButton) {
    backButton.addEventListener('click', function() {
      playMenuClickSound();
    });
  }

  // Add click sound to customize button
  const customizeButton = document.querySelector('.button-link');
  if (customizeButton) {
    customizeButton.addEventListener('click', function() {
      playMenuClickSound();
    });
  }
});