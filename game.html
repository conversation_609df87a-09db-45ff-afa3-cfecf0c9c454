<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Game</title>
    
    <link rel="stylesheet" href="game.css">
</head>
<body>
    <div class="background"></div>
    <div class="logo"></div>
    <div class="girl-character"></div>
    <div class="menu"></div>
    
    <a href="./main menu/start.html" id="startButton" class="press-start">>> Press Start</a>

    <!-- Audio Status Indicator -->
    <div id="audioStatus" style="position: fixed; top: 10px; right: 10px; background: rgba(0,0,0,0.7); color: white; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; z-index: 1000;">
        🎵 Audio: Loading...
    </div>

    <!-- Click Instruction (will be shown if needed) -->
    <div id="clickInstruction" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0,0,0,0.8); color: white; padding: 20px; border-radius: 10px; font-family: 'Press Start 2P', cursive; font-size: 16px; text-align: center; z-index: 2000; display: none; border: 2px solid #fff;">
        🎵 CLICK ANYWHERE TO START MUSIC 🎵<br>
        <small style="font-size: 10px; opacity: 0.8;">(Browser requires user interaction for audio)</small>
    </div>

    <script src="shared-audio-manager.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎵 Game.html: DOM loaded - initializing audio');

            // Initialize the shared audio manager
            window.SharedAudioManager.initialize();

            // Add animation for "Press Start"
            const pressStart = document.querySelector('.press-start');
            if (pressStart) {
                setInterval(() => {
                    pressStart.style.opacity = pressStart.style.opacity === '0.5' ? '1' : '0.5';
                }, 1000);
                console.log('✅ Press Start button animation started');
            } else {
                console.log('❌ Press Start button not found');
            }
        });
    </script>
</body>
</html>
