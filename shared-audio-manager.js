// Shared Audio Manager for Press Start Music
// This file manages the press start audio across all pages

(function() {
    'use strict';
    
    // Global audio state
    let pressStartAudio = null;
    let audioState = {
        isLoaded: false,
        isPlaying: false,
        currentTime: 0,
        volume: 0.7,
        shouldPlay: false
    };

    // Multiple paths to try for the press start audio
    const pressStartAudioPaths = [
        "Press start.mp3",
        "./Press start.mp3", 
        "../Press start.mp3",
        "../../Press start.mp3",
        "../../../Press start.mp3"
    ];

    // Update audio status indicator if it exists
    function updateAudioStatus(message) {
        const statusElement = document.getElementById('audioStatus');
        if (statusElement) {
            statusElement.textContent = message;
        }
    }

    // Show click instruction
    function showClickInstruction() {
        const clickElement = document.getElementById('clickInstruction');
        if (clickElement) {
            clickElement.style.display = 'block';
            console.log('🎵 Showing click instruction to user');
        }
    }

    // Hide click instruction
    function hideClickInstruction() {
        const clickElement = document.getElementById('clickInstruction');
        if (clickElement) {
            clickElement.style.display = 'none';
            console.log('🎵 Hiding click instruction');
        }
    }

    // Save audio state to localStorage
    function saveAudioState() {
        if (pressStartAudio) {
            const state = {
                currentTime: pressStartAudio.currentTime,
                volume: pressStartAudio.volume,
                isPlaying: !pressStartAudio.paused,
                shouldPlay: true,
                timestamp: Date.now()
            };
            localStorage.setItem('pressStartAudioState', JSON.stringify(state));
            console.log('🎵 Audio state saved:', state);
        }
    }

    // Load audio state from localStorage
    function loadAudioState() {
        const savedState = localStorage.getItem('pressStartAudioState');
        if (savedState) {
            try {
                const state = JSON.parse(savedState);
                // Only use saved state if it's recent (within 10 seconds)
                if (Date.now() - state.timestamp < 10000) {
                    console.log('🎵 Audio state loaded:', state);
                    return state;
                }
            } catch (e) {
                console.log('🎵 Failed to parse saved audio state');
            }
        }
        return null;
    }

    // Clear audio state
    function clearAudioState() {
        localStorage.removeItem('pressStartAudioState');
        console.log('🎵 Audio state cleared');
    }

    // Load and play press start audio
    function loadAndPlayPressStartAudio() {
        if (pressStartAudio) {
            console.log('🎵 Press start audio already exists');
            return;
        }

        let pathIndex = 0;

        function tryNextPath() {
            if (pathIndex >= pressStartAudioPaths.length) {
                console.error('🎵 Could not load press start audio from any path');
                updateAudioStatus('🎵 Audio: Failed to load ❌');
                return;
            }

            const audioPath = pressStartAudioPaths[pathIndex];
            console.log(`🎵 Trying audio path: ${audioPath}`);
            updateAudioStatus(`🎵 Audio: Trying path ${pathIndex + 1}/${pressStartAudioPaths.length}`);
            
            pressStartAudio = new Audio(audioPath);
            pressStartAudio.preload = "auto";
            pressStartAudio.volume = 0.7;
            pressStartAudio.loop = true;

            pressStartAudio.addEventListener('canplaythrough', () => {
                console.log(`🎵 Press start audio loaded successfully from: ${audioPath}`);
                updateAudioStatus('🎵 Audio: Loaded, trying to play...');
                audioState.isLoaded = true;

                // Check if we should restore previous state
                const savedState = loadAudioState();
                if (savedState && savedState.shouldPlay) {
                    pressStartAudio.currentTime = savedState.currentTime || 0;
                    pressStartAudio.volume = savedState.volume || 0.7;
                    console.log(`🎵 Restoring audio from time: ${savedState.currentTime}`);
                }

                // Try to play the audio
                pressStartAudio.play().then(() => {
                    console.log('🎵 Press start audio started playing');
                    updateAudioStatus('🎵 Audio: Playing ✅');
                    hideClickInstruction();
                    audioState.isPlaying = true;
                    audioState.shouldPlay = true;
                }).catch(error => {
                    console.log('🎵 Could not auto-play (browser policy):', error.name, error.message);
                    updateAudioStatus('🎵 Audio: Ready - CLICK ANYWHERE TO PLAY');
                    audioState.shouldPlay = true;

                    // Show prominent click instruction
                    setTimeout(() => {
                        showClickInstruction();
                        updateAudioStatus('🎵 Audio: 👆 CLICK THE PAGE TO START MUSIC');
                    }, 1000);
                });
            });

            pressStartAudio.addEventListener('error', (e) => {
                console.log(`🎵 Failed to load from path ${pathIndex}: ${audioPath}`);
                pathIndex++;
                tryNextPath();
            });

            pressStartAudio.addEventListener('timeupdate', () => {
                audioState.currentTime = pressStartAudio.currentTime;
            });
        }

        tryNextPath();
    }

    // Play press start audio
    function playPressStartAudio() {
        if (pressStartAudio && pressStartAudio.paused) {
            pressStartAudio.play().then(() => {
                console.log('🎵 Press start audio resumed');
                updateAudioStatus('🎵 Audio: Playing ✅');
                hideClickInstruction();
                audioState.isPlaying = true;
                audioState.shouldPlay = true;
            }).catch(error => {
                console.log('🎵 Could not play press start audio:', error);
                updateAudioStatus('🎵 Audio: Failed to play ❌');
            });
        }
    }

    // Stop press start audio
    function stopPressStartAudio() {
        if (pressStartAudio) {
            pressStartAudio.pause();
            pressStartAudio.currentTime = 0;
            console.log('🎵 Press start audio stopped');
            updateAudioStatus('🎵 Audio: Stopped ⏹️');
            audioState.isPlaying = false;
            audioState.shouldPlay = false;
            clearAudioState();
        }
    }

    // Get press start audio instance
    function getPressStartAudio() {
        return pressStartAudio;
    }

    // Initialize audio system
    function initializeAudio() {
        console.log('🎵 Initializing shared audio manager');
        
        // Load audio if not already loaded
        if (!pressStartAudio) {
            loadAndPlayPressStartAudio();
        }

        // Set up user interaction handler
        document.addEventListener('click', function enableAudio() {
            console.log('🖱️ User clicked - attempting to start audio');
            if (pressStartAudio && pressStartAudio.paused && audioState.shouldPlay) {
                playPressStartAudio();
            }
        }, { once: true });

        // Save state before page unloads
        window.addEventListener('beforeunload', saveAudioState);
    }

    // Make functions available globally
    window.SharedAudioManager = {
        initialize: initializeAudio,
        play: playPressStartAudio,
        stop: stopPressStartAudio,
        getAudio: getPressStartAudio,
        saveState: saveAudioState,
        loadState: loadAudioState,
        clearState: clearAudioState
    };

    // Legacy compatibility
    window.pressStartAudio = pressStartAudio;
    window.stopPressStartAudio = stopPressStartAudio;
    window.getPressStartAudio = getPressStartAudio;

    console.log('🎵 Shared Audio Manager loaded');
})();
