<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Memory Card Master</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap">
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap" rel="stylesheet">
</head>

<body>
    <div id="loading-screen" class="loading-screen">
        <div class="loader"></div>
        <p>Loading game assets...</p>
    </div>

    <audio id="flip-sound" src="https://assets.mixkit.co/sfx/preview/mixkit-game-click-1114.mp3" preload="auto"></audio>
    <audio id="match-sound" src="https://assets.mixkit.co/sfx/preview/mixkit-achievement-bell-600.mp3"
        preload="auto"></audio>
    <audio id="win-sound" src="https://assets.mixkit.co/sfx/preview/mixkit-winning-chimes-2015.mp3"
        preload="auto"></audio>
    <audio id="reward-sound" src="../../../../../Sound effects/Reward unlocked.wav" preload="auto"></audio>
    <audio id="lofi-music" src="sound/lofi.mp3" preload="auto" loop></audio>

    <div class="container">
        <div id="welcome-screen" class="screen hidden">
            <div class="welcome-content">
                <h1>Daka Remembers</h1>
                <div class="instructions">
                    <h2>Instructions</h2>
                    <p>1. Click on cards to flip them over</p>
                    <p>2. Remember what's on each card</p>
                    <p>3. Match all pairs of cards to win</p>
                    <div class="difficulty-section">
                        <h3>Choose Difficulty:</h3>
                        <div class="difficulty-buttons">
                            <button id="easy-btn" class="difficulty-btn">
                                <span>Easy (4 pairs)</span>
                            </button>
                            <button id="medium-btn" class="difficulty-btn">
                                <span>Medium (6 pairs)</span>
                            </button>
                            <button id="hard-btn" class="difficulty-btn">
                                <span>Hard (8 pairs)</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="game-screen" class="screen hidden">
            <div class="game-header">
                <div class="level-info">
                    <span class="label">Difficulty:</span>
                    <span id="difficulty-display" class="value">Easy</span>
                </div>
                <div class="stats">
                    <div class="stat">
                        <span class="label">Moves</span>
                        <span id="moves" class="value">0</span>
                    </div>
                    <div class="stat">
                        <span class="label">Pairs</span>
                        <span id="pairs" class="value">0</span>
                        <span class="label">/</span>
                        <span id="total-pairs" class="value">4</span>
                    </div>
                </div>
                <button id="reset-btn" class="reset-btn">Reset</button>
                <button id="music-toggle" class="music-toggle" title="Toggle Background Music">🎵</button>
            </div>
            <div id="game-board" class="game-board"></div>
            <button id="back-btn" class="back-btn">
                <span class="icon">←</span> Back to Menu
            </button>
        </div>

        <div id="win-screen" class="screen hidden">
            <div class="win-content">
                <div class="win-text-section">
                    <h1>Congratulations!</h1>
                    <p class="win-message">You've matched all pairs!</p>
                </div>
                <div class="celebration-gif">
                    <img id="win-image" src="img/thump.gif"
                        alt="Celebration">
                </div>
                <div class="final-stats">
                    <div class="final-stat">
                        <span class="label">Total Moves:</span>
                        <span id="total-moves" class="value">0</span>
                    </div>
                    <div class="final-stat">
                        <span class="label">Difficulty:</span>
                        <span id="final-difficulty" class="value">Easy</span>
                    </div>
                </div>
                <div class="win-buttons">
                    <button id="play-again-btn" class="play-again-btn">Replay</button>
                    <button id="win-back-btn" class="back-btn">
                        Next
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>

</html>