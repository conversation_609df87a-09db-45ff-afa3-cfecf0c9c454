body {
    margin: 0;
    overflow: hidden;
    font-family: 'Press Start 2P', cursive;
    height: 100vh;
}

/* Fixed Background */
#background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    object-fit: cover;
    z-index: -1;
    image-rendering: pixelated;
}

/* Game Container */
#game-container {
    position: relative;
    width: 800px;
    height: 600px;
    margin: 0 auto;
    overflow: hidden;
}

/* Dialogue Box */
#dialogue-box {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    max-width: 700px;
    background-color: rgba(255, 255, 255, 0.9);
    border: 4px solid #FFD700;
    padding: 20px;
    border-radius: 10px;
    color: #000;
    font-size: 14px;
    text-align: center;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
    z-index: 100;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease;
}

#dialogue-box.visible {
    opacity: 1;
    visibility: visible;
}

#next-button {
    margin-top: 15px;
    padding: 10px 20px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-family: 'Press Start 2P', cursive;
    font-size: 12px;
    transition: background-color 0.2s;
}

#next-button:hover {
    background-color: #45a049;
}