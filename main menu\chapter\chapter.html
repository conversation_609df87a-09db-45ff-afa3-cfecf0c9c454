<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pixel Square Game Chapters</title>
    <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="chapter.css">
</head>

<body>
    <button class="back-button" onclick="window.location.href='../start.html'">← BACK</button>
    <div class="game-container">
        <h1>CHAPTERS</h1>
        <div class="levels-rack">
            <div class="rope"></div>
            <div class="levels-container">
                <div class="level-circle" data-level="1">
                    <img src="./images/level1.jpg" alt="Chapter 1">
                </div>
                <div class="connector-rope"></div>
                <div class="level-circle locked" data-level="2">
                    <img src="./images/level2.jpg" alt="Chapter 2">
                </div>
                <div class="connector-rope"></div>
                <div class="level-circle locked" data-level="3">
                    <img src="./images/level3.jpg" alt="Chapter 3">
                </div>
                <div class="connector-rope"></div>
                <div class="level-circle locked" data-level="4">
                    <img src="./images/level4.jpg" alt="Chapter 4">
                </div>
                <div class="connector-rope"></div>
                <div class="level-circle locked" data-level="5">
                    <img src="./images/level5.jpg" alt="Chapter 5">
                </div>
            </div>
            <div class="rope"></div>
        </div>
        <div class="level-info">
            <div class="current-level-display" id="currentLevel"></div>
            <p id="levelDescription">CHOOSE A CHAPTER!</p>
        </div>
    </div>

    <script src="chapter.js"></script>
</body>

</html>
