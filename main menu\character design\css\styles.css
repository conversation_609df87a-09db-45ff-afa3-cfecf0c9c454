@import url('https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap');

body, html {
  height: 100%;
  width: 100%;
  margin: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #000;
  font-family: 'Press Start 2P', cursive;
  position: relative;
}

/* Back button styling */
.back-button {
  position: absolute;
  top: 20px;
  left: 20px;
  background-color: #00bfff;
  color: black;
  border: 3px solid #000;
  padding: 12px 20px;
  font-family: 'Press Start 2P', cursive;
  font-size: 0.8rem;
  cursor: pointer;
  border-radius: 25px;
  transition: all 0.2s ease;
  text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.5);
  box-shadow:
      inset 2px 2px 0 rgba(255, 255, 255, 0.3),
      inset -2px -2px 0 rgba(0, 0, 0, 0.3),
      0 4px 8px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  letter-spacing: 1px;
}

.back-button:hover {
  background-color: #1e90ff;
  transform: translateY(-2px);
  box-shadow:
      inset 2px 2px 0 rgba(255, 255, 255, 0.4),
      inset -2px -2px 0 rgba(0, 0, 0, 0.4),
      0 6px 12px rgba(0, 0, 0, 0.4);
}

.back-button:active {
  transform: translateY(0px);
  box-shadow:
      inset 2px 2px 0 rgba(255, 255, 255, 0.2),
      inset -2px -2px 0 rgba(0, 0, 0, 0.5),
      0 2px 4px rgba(0, 0, 0, 0.3);
}

.background-wrapper {
  position: relative;
  width: 95vw;
  max-width: 1200px;
}

.background-wrapper img.bg {
  width: 100%;
}

/* Standardized card positioning */
.card {
  position: absolute;
  top: 25%;
  left: 8%;
  width: 30%;
  transition: transform 0.3s ease;
}

.card img {
  width: 100%;
}

.button-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.button-link {
  display: block;
  width: 90vw;
  max-width: 1000px;
}

.background-wrapper img.bg {
  width: 100%;
}

/* Standardized card positioning */
.card {
  position: absolute;
  top: 25%;
  left: 8%;
  width: 25%;
  transition: transform 0.3s ease;
}

.card img {
  width: 100%;
}

.button-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.button-link {
  display: block;
  text-decoration: none;
}

.button {
  position: absolute;
  top: 75%;
  width: 20%;
  transition: transform 0.3s ease;
  z-index: 10;
}

.button img {
  width: 100%;
  display: block;
}

.button:hover {
  transform: scale(1.1);
}



/* Custom popup message */
.popup {
  display: none;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #4CAF50;
  color: white;
  padding: 25px 40px;
  border-radius: 8px;
  text-align: center;
  z-index: 1000;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  animation: fadeIn 0.3s;
  font-family: 'Press Start 2P', cursive;
  font-size: 16px;
  font-weight: bold;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}


















