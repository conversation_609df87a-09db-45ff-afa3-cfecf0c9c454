<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Game Menu</title>
    <link rel="stylesheet" href="start.css">
    <link rel="stylesheet" href="../main menu/playerprofile/playerprofile.css">
    <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap" rel="stylesheet">
</head>
<body>
    
    <!-- Main Menu Screen -->
    <div id="mainMenu">
        <div class="menu-buttons">
            <button class="menu-button" onclick="window.location.href='chapter/chapter.html'">START GAME</button>
            <button class="menu-button" onclick="window.location.href='playerprofile/playerprofile.html'">PLAYER PROFILE</button>
            <button class="menu-button" onclick="window.location.href='character design/index.html'">CHARACTER DESIGN</button>
            <button class="menu-button"onclick="window.location.href='setting/index.html'">SETTINGS</button>
        </div>
    </div>

    <!-- Audio Status Indicator -->
    <div id="audioStatus" style="position: fixed; top: 10px; right: 10px; background: rgba(0,0,0,0.7); color: white; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; z-index: 1000;">
        🎵 Main Menu Audio: Loading...
    </div>

    <script src="start.js"></script>
    <script src="../main menu/playerprofile/playerprofile.js"></script>
</body>
</html>
