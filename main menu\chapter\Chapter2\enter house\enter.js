const player = document.getElementById("player");

// Video elements for messages
let firstMessageVideo = null;
let secondMessageVideo = null;
let thirdMessageVideo = null;
let fourthMessageVideo = null;
let fifthMessageVideo = null;
let sixthMessageVideo = null;

// Messages array (removed duplicate)
const messages = [
  "Listen closely, traveler.",
  "The Sinmo was once a compassionate woman, beloved by our village.",
  "She tended to the sick and nurtured the land. But jealousy and misunderstanding turned her heart to darkness.",
  "The people wronged her, casting her aside when she needed them most. In her pain, she became the very thing we fear.",
  "To subdue the Sinmo and restore peace, you must collect the relic from the chorten. It holds the power to heal her spirit.",
  "Will you help us bring her back?"
];

let messageIndex = 0;
let isTyping = false; // Prevent button spam during typing
let typingInterval = null; // Store the typing interval
let currentMessage = ""; // Store the current message being typed
const textElement = document.getElementById("dialogue-text");
const nextBtn = document.getElementById("next-btn");
const continueBtn = document.getElementById("continue-btn");
const choiceContainer = document.getElementById("choice-container");
const yesBtn = document.getElementById("yes-btn");
const noBtn = document.getElementById("no-btn");

// Scroll popup elements
const scrollPopup = document.getElementById("scroll-popup");
const scrollNextBtn = document.getElementById("scroll-next-btn");
const dialogueBox = document.getElementById("dialogue-box");

// Load first message video
function loadFirstMessageVideo() {
  firstMessageVideo = document.createElement('video');
  firstMessageVideo.src = '5.mp4';
  firstMessageVideo.volume = 0.7;
  firstMessageVideo.preload = 'auto';
  firstMessageVideo.style.display = 'none'; // Hidden video element

  firstMessageVideo.addEventListener('canplaythrough', () => {
    console.log('First message video (5.mp4) loaded successfully');
  });

  firstMessageVideo.addEventListener('error', (e) => {
    console.log('Failed to load first message video (5.mp4):', e);
  });

  // Add to document
  document.body.appendChild(firstMessageVideo);
}

// Load second message video
function loadSecondMessageVideo() {
  secondMessageVideo = document.createElement('video');
  secondMessageVideo.src = '6.mp4';
  secondMessageVideo.volume = 0.7;
  secondMessageVideo.preload = 'auto';
  secondMessageVideo.style.display = 'none'; // Hidden video element

  secondMessageVideo.addEventListener('canplaythrough', () => {
    console.log('Second message video (6.mp4) loaded successfully');
  });

  secondMessageVideo.addEventListener('error', (e) => {
    console.log('Failed to load second message video (6.mp4):', e);
  });

  // Add to document
  document.body.appendChild(secondMessageVideo);
}

// Load third message video
function loadThirdMessageVideo() {
  thirdMessageVideo = document.createElement('video');
  thirdMessageVideo.src = '7.mp4';
  thirdMessageVideo.volume = 0.7;
  thirdMessageVideo.preload = 'auto';
  thirdMessageVideo.style.display = 'none'; // Hidden video element

  thirdMessageVideo.addEventListener('canplaythrough', () => {
    console.log('Third message video (7.mp4) loaded successfully');
  });

  thirdMessageVideo.addEventListener('error', (e) => {
    console.log('Failed to load third message video (7.mp4):', e);
  });

  // Add to document
  document.body.appendChild(thirdMessageVideo);
}

// Load fourth message video
function loadFourthMessageVideo() {
  fourthMessageVideo = document.createElement('video');
  fourthMessageVideo.src = '8.mp4';
  fourthMessageVideo.volume = 0.7;
  fourthMessageVideo.preload = 'auto';
  fourthMessageVideo.style.display = 'none'; // Hidden video element

  fourthMessageVideo.addEventListener('canplaythrough', () => {
    console.log('Fourth message video (8.mp4) loaded successfully');
  });

  fourthMessageVideo.addEventListener('error', (e) => {
    console.log('Failed to load fourth message video (8.mp4):', e);
  });

  // Add to document
  document.body.appendChild(fourthMessageVideo);
}

// Load fifth message video
function loadFifthMessageVideo() {
  fifthMessageVideo = document.createElement('video');
  fifthMessageVideo.src = '9.mp4';
  fifthMessageVideo.volume = 0.7;
  fifthMessageVideo.preload = 'auto';
  fifthMessageVideo.style.display = 'none'; // Hidden video element

  fifthMessageVideo.addEventListener('canplaythrough', () => {
    console.log('Fifth message video (9.mp4) loaded successfully');
  });

  fifthMessageVideo.addEventListener('error', (e) => {
    console.log('Failed to load fifth message video (9.mp4):', e);
  });

  // Add to document
  document.body.appendChild(fifthMessageVideo);
}

// Load sixth message video
function loadSixthMessageVideo() {
  sixthMessageVideo = document.createElement('video');
  sixthMessageVideo.src = '10.mp4';
  sixthMessageVideo.volume = 0.7;
  sixthMessageVideo.preload = 'auto';
  sixthMessageVideo.style.display = 'none'; // Hidden video element

  sixthMessageVideo.addEventListener('canplaythrough', () => {
    console.log('Sixth message video (10.mp4) loaded successfully');
  });

  sixthMessageVideo.addEventListener('error', (e) => {
    console.log('Failed to load sixth message video (10.mp4):', e);
  });

  // Add to document
  document.body.appendChild(sixthMessageVideo);
}

// Function to stop all videos
function stopAllVideos() {
  if (firstMessageVideo) {
    firstMessageVideo.pause();
    firstMessageVideo.currentTime = 0;
  }
  if (secondMessageVideo) {
    secondMessageVideo.pause();
    secondMessageVideo.currentTime = 0;
  }
  if (thirdMessageVideo) {
    thirdMessageVideo.pause();
    thirdMessageVideo.currentTime = 0;
  }
  if (fourthMessageVideo) {
    fourthMessageVideo.pause();
    fourthMessageVideo.currentTime = 0;
  }
  if (fifthMessageVideo) {
    fifthMessageVideo.pause();
    fifthMessageVideo.currentTime = 0;
  }
  if (sixthMessageVideo) {
    sixthMessageVideo.pause();
    sixthMessageVideo.currentTime = 0;
  }
}

// Target Y position (where player should stop near housewife)
const stopAt = 350; // Increased distance for more space

// Move player upwards (simulate walking)
function moveUp() {
  let topPosition = parseInt(window.getComputedStyle(player).top);

  const interval = setInterval(() => {
    if (topPosition <= stopAt) {
      clearInterval(interval);
      console.log("Player reached the housewife.");
      // Show scroll popup after player stops
      setTimeout(() => {
        showScrollPopup();
      }, 500); // Small delay after stopping
      return;
    }

    topPosition -= 2; // speed
    player.style.top = topPosition + "px";
  }, 20); // frame rate
}

// Show scroll popup function
function showScrollPopup() {
  scrollPopup.style.display = 'block';
  setTimeout(() => {
    scrollPopup.classList.add('show');
  }, 100);
}

// Display message instantly (no typewriter effect)
function typeMessage(message, callback) {
  if (isTyping) return; // Prevent overlapping

  isTyping = true;
  currentMessage = message; // Store the current message

  // Display message instantly
  textElement.innerHTML = message;

  // Play video for the first message
  if (messageIndex === 0 && firstMessageVideo) {
    // Stop all videos first, then play the first message video
    stopAllVideos();
    console.log('Playing 5.mp4 for first message');
    firstMessageVideo.currentTime = 0;
    firstMessageVideo.play().then(() => {
      console.log('First message video (5.mp4) played successfully');
    }).catch(error => {
      console.log('Could not play first message video (5.mp4):', error);
    });
  }

  isTyping = false;
  if (callback) callback();
}

// Function to complete typing instantly (no longer needed since we display instantly)
function completeTyping() {
  // Since we display messages instantly, this function is no longer needed
  // but kept for compatibility with existing button handlers
  isTyping = false;
}

// Start dialogue system
function startDialogue() {
  // Hide scroll popup
  scrollPopup.style.display = 'none';

  // Show dialogue box
  dialogueBox.style.display = 'block';

  // Set up text styling
  textElement.style.fontFamily = "'Press Start 2P', cursive";
  textElement.style.fontSize = "12px";
  textElement.style.color = "black";
  textElement.style.textAlign = "center";

  // Display first message
  typeMessage(messages[messageIndex]);
}

// Scroll next button handler
scrollNextBtn.addEventListener("click", () => {
  startDialogue();
});

// Next button handler
nextBtn.addEventListener("click", () => {
  // Stop all videos before advancing to next message
  stopAllVideos();

  // Play video immediately when Next button is pressed based on current message
  if (messageIndex === 0 && secondMessageVideo) {
    console.log('Playing 6.mp4 immediately on Next button press');
    secondMessageVideo.currentTime = 0;
    secondMessageVideo.play().then(() => {
      console.log('Second message video (6.mp4) played successfully on button press');
    }).catch(error => {
      console.log('Could not play second message video (6.mp4) on button press:', error);
    });
  } else if (messageIndex === 1 && thirdMessageVideo) {
    console.log('Playing 7.mp4 immediately on Next button press');
    thirdMessageVideo.currentTime = 0;
    thirdMessageVideo.play().then(() => {
      console.log('Third message video (7.mp4) played successfully on button press');
    }).catch(error => {
      console.log('Could not play third message video (7.mp4) on button press:', error);
    });
  } else if (messageIndex === 2 && fourthMessageVideo) {
    console.log('Playing 8.mp4 immediately on Next button press');
    fourthMessageVideo.currentTime = 0;
    fourthMessageVideo.play().then(() => {
      console.log('Fourth message video (8.mp4) played successfully on button press');
    }).catch(error => {
      console.log('Could not play fourth message video (8.mp4) on button press:', error);
    });
  } else if (messageIndex === 3 && fifthMessageVideo) {
    console.log('Playing 9.mp4 immediately on Next button press');
    fifthMessageVideo.currentTime = 0;
    fifthMessageVideo.play().then(() => {
      console.log('Fifth message video (9.mp4) played successfully on button press');
    }).catch(error => {
      console.log('Could not play fifth message video (9.mp4) on button press:', error);
    });
  } else if (messageIndex === 4 && sixthMessageVideo) {
    console.log('Playing 10.mp4 immediately on Next button press');
    sixthMessageVideo.currentTime = 0;
    sixthMessageVideo.play().then(() => {
      console.log('Sixth message video (10.mp4) played successfully on button press');
    }).catch(error => {
      console.log('Could not play sixth message video (10.mp4) on button press:', error);
    });
  }

  // Advance to next message
  messageIndex++;
  if (messageIndex < messages.length) {
    typeMessage(messages[messageIndex]);
  }

  // Show Yes/No buttons on last message (when asking "Will you help us bring her back?")
  if (messageIndex === messages.length - 1) {
    nextBtn.style.display = "none";
    choiceContainer.style.display = "flex";
  }
});

// Yes button handler
yesBtn.addEventListener("click", () => {
  // Stop all videos first
  stopAllVideos();

  // Show loading screen before redirecting to next scene
  const loadingOverlay = document.createElement('div');
  loadingOverlay.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    font-family: 'Press Start 2P', cursive;
    color: white;
    font-size: 24px;
  `;

  const loadingText = document.createElement('div');
  loadingText.textContent = 'Loading...';
  loadingOverlay.appendChild(loadingText);

  const spinner = document.createElement('div');
  spinner.style.cssText = `
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 2s linear infinite;
    margin-top: 20px;
  `;
  loadingOverlay.appendChild(spinner);

  // Add spinner animation
  const style = document.createElement('style');
  style.textContent = `
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `;
  document.head.appendChild(style);

  document.body.appendChild(loadingOverlay);

  // Redirect after 3 seconds
  setTimeout(() => {
    window.location.href = "Memory Game/index.html";
  }, 3000);
});

// No button handler
noBtn.addEventListener("click", () => {
  // Stop all videos first
  stopAllVideos();

  // Show goodbye message
  textElement.innerHTML = "I understand. Farewell, traveler...";
  choiceContainer.style.display = "none";

  // Redirect back to chapter selection after delay
  setTimeout(() => {
    window.location.href = "../../chapter.html";
  }, 2000);
});

// Initialize everything when page loads
window.onload = () => {
  // Load the message videos
  loadFirstMessageVideo();
  loadSecondMessageVideo();
  loadThirdMessageVideo();
  loadFourthMessageVideo();
  loadFifthMessageVideo();
  loadSixthMessageVideo();

  // Start player movement
  moveUp();
};

// Stop all videos when page is about to unload
window.addEventListener('beforeunload', () => {
  stopAllVideos();
});

// Stop all videos when page loses focus
window.addEventListener('blur', () => {
  stopAllVideos();
});
