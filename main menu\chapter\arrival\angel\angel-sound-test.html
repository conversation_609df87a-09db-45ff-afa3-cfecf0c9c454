<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Angel Page Transformation Sound Test</title>
</head>
<body>
  <h1>Angel Page Transformation Sound Test</h1>
  <button id="test-btn">Test Transformation Sound</button>
  <div id="status"></div>

  <script>
    const statusDiv = document.getElementById('status');
    const testBtn = document.getElementById('test-btn');
    
    // Test different paths from angel folder
    const soundPaths = [
      "../../../Sound effects/Sinmo transformation .mp3",
      "../../Sound effects/Sinmo transformation .mp3",
      "../Sound effects/Sinmo transformation .mp3",
      "./Sound effects/Sinmo transformation .mp3",
      "../../../../Sound effects/Sinmo transformation .mp3"
    ];
    
    let transformationSound;
    
    function testPath(pathIndex) {
      if (pathIndex >= soundPaths.length) {
        statusDiv.innerHTML += '<br>All paths failed!';
        return;
      }
      
      const path = soundPaths[pathIndex];
      statusDiv.innerHTML += `<br>Testing path ${pathIndex + 1}: ${path}`;
      
      transformationSound = new Audio(path);
      transformationSound.volume = 0.8;
      
      transformationSound.addEventListener('canplaythrough', () => {
        statusDiv.innerHTML += `<br>✓ Path ${pathIndex + 1} loaded successfully!`;
        statusDiv.innerHTML += `<br>Click the button to test playback.`;
      });
      
      transformationSound.addEventListener('error', (e) => {
        statusDiv.innerHTML += `<br>✗ Path ${pathIndex + 1} failed to load`;
        testPath(pathIndex + 1);
      });
    }
    
    testBtn.addEventListener('click', () => {
      if (transformationSound) {
        transformationSound.currentTime = 0;
        transformationSound.play().then(() => {
          statusDiv.innerHTML += '<br>✓ Transformation sound played successfully!';
        }).catch(error => {
          statusDiv.innerHTML += `<br>✗ Playback failed: ${error}`;
        });
      } else {
        statusDiv.innerHTML += '<br>No sound loaded to test';
      }
    });
    
    // Start testing
    statusDiv.innerHTML = 'Starting transformation sound path tests from angel folder...';
    testPath(0);
  </script>
</body>
</html>
