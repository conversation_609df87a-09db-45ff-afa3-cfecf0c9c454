// Global audio variable for press start music
let pressStartAudio;

// Load and play press start audio
function loadAndPlayPressStartAudio() {
    const audioPath = "Press start.mp3";
    pressStartAudio = new Audio(audioPath);
    pressStartAudio.preload = "auto";
    pressStartAudio.volume = 0.7;
    pressStartAudio.loop = true; // Loop the audio

    pressStartAudio.addEventListener('canplaythrough', () => {
        console.log('Press start audio loaded successfully');
        // Play the audio automatically when loaded
        pressStartAudio.play().catch(error => {
            console.log('Could not auto-play press start audio:', error);
        });
    });

    pressStartAudio.addEventListener('error', (e) => {
        console.error('Failed to load press start audio:', e);
    });
}

// Stop press start audio
function stopPressStartAudio() {
    if (pressStartAudio) {
        pressStartAudio.pause();
        pressStartAudio.currentTime = 0;
        console.log('Press start audio stopped');
    }
}

// Make audio available globally for other pages
window.pressStartAudio = pressStartAudio;
window.stopPressStartAudio = stopPressStartAudio;

document.addEventListener('DOMContentLoaded', () => {
    // Load and play press start audio when game.html loads
    loadAndPlayPressStartAudio();

    // Add animation for "Press Start"
    const pressStart = document.querySelector('.press-start');
    setInterval(() => {
        pressStart.style.opacity = pressStart.style.opacity === '0.5' ? '1' : '0.5';
    }, 1000);

    // Enable audio context on first user interaction
    document.addEventListener('click', function enableAudio() {
        if (pressStartAudio && pressStartAudio.paused) {
            // Try to play the audio if it's paused
            pressStartAudio.play().catch(error => {
                console.log('Could not play press start audio on user interaction:', error);
            });
        }
        // Remove this listener after first interaction
        document.removeEventListener('click', enableAudio);
    }, { once: true });
});
