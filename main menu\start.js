// Sound manager for menu clicks and press start audio
let menuClickSound;
let pressStartAudio;

const menuSoundPaths = [
    "../Sound effects/Click menu and settings .mp3",
    "./Sound effects/Click menu and settings .mp3",
    "Sound effects/Click menu and settings .mp3",
    "../Sound effects/click-menu.mp3",
    "./Sound effects/click-menu.mp3",
    "Sound effects/click-menu.mp3"
];

// Multiple paths to try for the press start audio from main menu directory
const pressStartAudioPaths = [
    "../Press start.mp3",
    "../../Press start.mp3",
    "./Press start.mp3",
    "Press start.mp3"
];

// Load menu click sound
function loadMenuClickSound() {
    let pathIndex = 0;

    function tryNextPath() {
        if (pathIndex >= menuSoundPaths.length) {
            console.error('Could not load menu click sound from any path');
            return;
        }

        menuClickSound = new Audio(menuSoundPaths[pathIndex]);
        menuClickSound.preload = "auto";
        menuClickSound.volume = 0.7;

        menuClickSound.addEventListener('canplaythrough', () => {
            console.log(`Menu click sound loaded successfully from: ${menuSoundPaths[pathIndex]}`);
        });

        menuClickSound.addEventListener('error', (e) => {
            console.log(`Failed to load menu sound from path ${pathIndex}: ${menuSoundPaths[pathIndex]}`);
            pathIndex++;
            tryNextPath();
        });
    }

    tryNextPath();
}

// Play menu click sound
function playMenuClickSound() {
    if (menuClickSound) {
        menuClickSound.currentTime = 0;
        menuClickSound.play().catch(error => {
            console.log('Could not play menu click sound:', error);
        });
    }
}

// Load and play press start audio for main menu
function loadAndPlayPressStartAudio() {
    let pathIndex = 0;

    function tryNextPath() {
        if (pathIndex >= pressStartAudioPaths.length) {
            console.error('Could not load press start audio from any path in main menu');
            updateAudioStatus('🎵 Main Menu Audio: Failed to load ❌');
            return;
        }

        const audioPath = pressStartAudioPaths[pathIndex];
        console.log(`🎵 Main Menu: Trying press start audio path: ${audioPath}`);

        pressStartAudio = new Audio(audioPath);
        pressStartAudio.preload = "auto";
        pressStartAudio.volume = 0.7;
        pressStartAudio.loop = true; // Loop the audio

        pressStartAudio.addEventListener('canplaythrough', () => {
            console.log(`🎵 Main Menu: Press start audio loaded successfully from: ${audioPath}`);
            updateAudioStatus('🎵 Main Menu Audio: Loaded, trying to play...');

            // Try to play the audio automatically when loaded
            pressStartAudio.play().then(() => {
                console.log('🎵 Main Menu: Press start audio started playing automatically');
                updateAudioStatus('🎵 Main Menu Audio: Playing ✅');
                // Make audio available globally
                window.pressStartAudio = pressStartAudio;
            }).catch(error => {
                console.log('🎵 Main Menu: Could not auto-play press start audio (browser policy):', error);
                console.log('🎵 Main Menu: Audio will play on first user interaction');
                updateAudioStatus('🎵 Main Menu Audio: Ready (click to play)');
            });
        });

        pressStartAudio.addEventListener('error', (e) => {
            console.log(`🎵 Main Menu: Failed to load press start audio from path ${pathIndex}: ${audioPath}`);
            updateAudioStatus(`🎵 Main Menu Audio: Failed path ${pathIndex + 1}/${pressStartAudioPaths.length}`);
            pathIndex++;
            tryNextPath();
        });
    }

    tryNextPath();
}

// Access press start audio from local, parent window or global scope
function getPressStartAudio() {
    // First try local audio (created in this page)
    if (pressStartAudio) {
        return pressStartAudio;
    }
    // Try to get from parent window (if in iframe)
    if (window.parent && window.parent.pressStartAudio) {
        return window.parent.pressStartAudio;
    }
    // Try to get from global scope
    if (window.pressStartAudio) {
        return window.pressStartAudio;
    }
    // Try to get from opener window
    if (window.opener && window.opener.pressStartAudio) {
        return window.opener.pressStartAudio;
    }
    return null;
}

// Continue press start audio if it exists and is paused
function continuePressStartAudio() {
    const pressStartAudio = getPressStartAudio();
    if (pressStartAudio && pressStartAudio.paused) {
        pressStartAudio.play().catch(error => {
            console.log('Could not continue press start audio:', error);
        });
    }
}

// Update audio status indicator
function updateAudioStatus(message) {
    const statusElement = document.getElementById('audioStatus');
    if (statusElement) {
        statusElement.textContent = message;
    }
}

// Make press start audio functions available globally
window.getPressStartAudio = getPressStartAudio;
window.stopPressStartAudio = function() {
    const audio = getPressStartAudio();
    if (audio) {
        audio.pause();
        audio.currentTime = 0;
        console.log('🎵 Press start audio stopped from main menu');
        updateAudioStatus('🎵 Main Menu Audio: Stopped ⏹️');
    }
};

// Wait for DOM to load
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎵 Main Menu: DOM loaded - starting audio systems');

    // Load menu click sound
    loadMenuClickSound();

    // Load and play press start audio when main menu loads
    loadAndPlayPressStartAudio();

    // Also try to continue any existing press start audio (fallback)
    setTimeout(() => {
        if (!pressStartAudio) {
            console.log('🎵 Main Menu: No local audio created, trying to continue existing audio');
            continuePressStartAudio();
        }
    }, 1000);

    // Enable audio context on first user interaction
    document.addEventListener('click', function enableAudio() {
        if (menuClickSound) {
            // Try to play and immediately pause to enable audio context
            menuClickSound.play().then(() => {
                menuClickSound.pause();
                menuClickSound.currentTime = 0;
            }).catch(() => {
                // Ignore errors during audio context initialization
            });
        }

        // Also try to continue press start audio on first interaction
        console.log('🖱️ Main Menu: User clicked - attempting to start audio');
        if (pressStartAudio && pressStartAudio.paused) {
            pressStartAudio.play().then(() => {
                console.log('🎵 Main Menu: Local press start audio started on user interaction');
                updateAudioStatus('🎵 Main Menu Audio: Playing ✅');
                window.pressStartAudio = pressStartAudio;
            }).catch(error => {
                console.log('🎵 Main Menu: Could not play local press start audio:', error);
                updateAudioStatus('🎵 Main Menu Audio: Failed to play ❌');
            });
        } else if (pressStartAudio && !pressStartAudio.paused) {
            console.log('🎵 Main Menu: Local press start audio already playing');
            updateAudioStatus('🎵 Main Menu Audio: Already playing ✅');
        } else {
            console.log('🎵 Main Menu: No local audio, trying to continue existing audio');
            continuePressStartAudio();
        }

        // Remove this listener after first interaction
        document.removeEventListener('click', enableAudio);
    }, { once: true });

    // Switch to main menu when start is pressed
    document.getElementById('startButton').addEventListener('click', function() {
        playMenuClickSound();
        document.getElementById('startScreen').style.display = 'none';
        document.getElementById('mainMenu').style.display = 'flex';
    });

    // Handle menu button clicks
    document.querySelectorAll('.menu-button').forEach(button => {
        button.addEventListener('click', function() {
            playMenuClickSound();
            const action = this.textContent.trim();

            switch(action) {
                case 'START GAME':
                    // Don't stop press start audio here - it should continue playing
                    // until a chapter is actually selected
                    window.location.href = 'chapter/chapter.html';
                    break;
                case 'PLAYER PROFILE':
                    // Add profile screen logic
                    break;
                case 'CHARACTER DESIGN':
                    // Add character design logic
                    break;
                case 'SETTINGS':
                    // Add settings logic
                    break;
            }
        });
    });
});
