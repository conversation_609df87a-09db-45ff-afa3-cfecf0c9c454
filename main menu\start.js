// Sound manager for menu clicks only
let menuClickSound;

const menuSoundPaths = [
    "../Sound effects/Click menu and settings .mp3",
    "./Sound effects/Click menu and settings .mp3",
    "Sound effects/Click menu and settings .mp3",
    "../Sound effects/click-menu.mp3",
    "./Sound effects/click-menu.mp3",
    "Sound effects/click-menu.mp3"
];

// Load menu click sound
function loadMenuClickSound() {
    let pathIndex = 0;

    function tryNextPath() {
        if (pathIndex >= menuSoundPaths.length) {
            console.error('Could not load menu click sound from any path');
            return;
        }

        menuClickSound = new Audio(menuSoundPaths[pathIndex]);
        menuClickSound.preload = "auto";
        menuClickSound.volume = 0.7;

        menuClickSound.addEventListener('canplaythrough', () => {
            console.log(`Menu click sound loaded successfully from: ${menuSoundPaths[pathIndex]}`);
        });

        menuClickSound.addEventListener('error', (e) => {
            console.log(`Failed to load menu sound from path ${pathIndex}: ${menuSoundPaths[pathIndex]}`);
            pathIndex++;
            tryNextPath();
        });
    }

    tryNextPath();
}

// Play menu click sound
function playMenuClickSound() {
    if (menuClickSound) {
        menuClickSound.currentTime = 0;
        menuClickSound.play().catch(error => {
            console.log('Could not play menu click sound:', error);
        });
    }
}



// Access press start audio from global scope or other windows
function getPressStartAudio() {
    // Try to get from global scope first
    if (window.pressStartAudio) {
        return window.pressStartAudio;
    }
    // Try to get from parent window (if in iframe)
    if (window.parent && window.parent.pressStartAudio) {
        return window.parent.pressStartAudio;
    }
    // Try to get from opener window
    if (window.opener && window.opener.pressStartAudio) {
        return window.opener.pressStartAudio;
    }
    return null;
}

// Continue press start audio if it exists and is paused
function continuePressStartAudio() {
    const pressStartAudio = getPressStartAudio();
    if (pressStartAudio && pressStartAudio.paused) {
        pressStartAudio.play().catch(error => {
            console.log('Could not continue press start audio:', error);
        });
    }
}

// Update audio status indicator
function updateAudioStatus(message) {
    const statusElement = document.getElementById('audioStatus');
    if (statusElement) {
        statusElement.textContent = message;
    }
}

// Make press start audio functions available globally
window.getPressStartAudio = getPressStartAudio;
window.stopPressStartAudio = function() {
    const audio = getPressStartAudio();
    if (audio) {
        audio.pause();
        audio.currentTime = 0;
        console.log('🎵 Press start audio stopped from main menu');
        updateAudioStatus('🎵 Main Menu Audio: Stopped ⏹️');
    }
};

// Wait for DOM to load
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎵 Main Menu: DOM loaded - starting audio systems');

    // Load menu click sound
    loadMenuClickSound();

    // Initialize the shared audio manager to continue the press start audio
    if (window.SharedAudioManager) {
        window.SharedAudioManager.initialize();
        console.log('🎵 Main Menu: Shared audio manager initialized');
    } else {
        console.log('🎵 Main Menu: Shared audio manager not found, trying fallback');
        continuePressStartAudio();
    }

    // Enable audio context on first user interaction
    document.addEventListener('click', function enableAudio() {
        if (menuClickSound) {
            // Try to play and immediately pause to enable audio context
            menuClickSound.play().then(() => {
                menuClickSound.pause();
                menuClickSound.currentTime = 0;
            }).catch(() => {
                // Ignore errors during audio context initialization
            });
        }

        // Try to continue press start audio on first interaction
        console.log('🖱️ Main Menu: User clicked - attempting to start audio');
        if (window.SharedAudioManager) {
            window.SharedAudioManager.play();
        } else {
            continuePressStartAudio();
        }

        // Remove this listener after first interaction
        document.removeEventListener('click', enableAudio);
    }, { once: true });

    // Switch to main menu when start is pressed
    document.getElementById('startButton').addEventListener('click', function() {
        playMenuClickSound();
        document.getElementById('startScreen').style.display = 'none';
        document.getElementById('mainMenu').style.display = 'flex';
    });

    // Handle menu button clicks
    document.querySelectorAll('.menu-button').forEach(button => {
        button.addEventListener('click', function() {
            playMenuClickSound();
            const action = this.textContent.trim();

            switch(action) {
                case 'START GAME':
                    // Don't stop press start audio here - it should continue playing
                    // until a chapter is actually selected
                    window.location.href = 'chapter/chapter.html';
                    break;
                case 'PLAYER PROFILE':
                    // Add profile screen logic
                    break;
                case 'CHARACTER DESIGN':
                    // Add character design logic
                    break;
                case 'SETTINGS':
                    // Add settings logic
                    break;
            }
        });
    });
});
