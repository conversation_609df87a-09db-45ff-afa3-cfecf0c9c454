<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Saved Character</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="css/styles.css">
</head>
<body>
  <button class="back-button" onclick="window.location.href='../start.html'">← BACK</button>

  <div class="background-wrapper">
    <img src="img/Frame 10.png" class="bg" alt="Background">

    <!-- Saved character image -->
    <div class="card">
      <img id="savedCharacter" src="img/Frame 11.png" alt="Saved Character">
    </div>

    <div class="button-container">

      <!-- Customize Button -->
      <a href="nonPlayer.html" class="button-link">
        <div class="button" style="left: 68%;">
          <img src="img/Frame8.png" alt="Customize Button">
        </div>
      </a>
    </div>
  </div>

  <script src="js/saved-script.js"></script>
</body>
</html>





