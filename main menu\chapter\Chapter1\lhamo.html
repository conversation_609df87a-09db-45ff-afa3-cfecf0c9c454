<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="lhamo.css">
        <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap" rel="stylesheet">

    <title>Girl Meets Man</title>
</head>
<body>
    <div id="scene">
        <div id="man"></div>
        <div id="girl"></div>
    </div>
    <div id="scroll-popup">
        <div id="scroll-content">
            <p class="letter">A local healer who has witnessed the lingo's effects on the villagers' health</p>
            <button id="nextButton" class="next-button">Next</button>
        </div>
    </div>

    <!-- Remove the Skip Button since we're using the Next button inside the scroll -->
    <!-- <button id="skipButton">Skip</button> -->
    
    <script src="lhamo.js"></script>
    <div class="left-stats">
        <div class="bar-group">
            <div class="bar health">
                <div class="bar-container">
                    <div class="bar-icon">❤️</div>
                    <div class="bar-segments" id="left-health-bar"></div>
                </div>
            </div>
            <div class="bar mana">
                <div class="bar-container">
                    <div class="bar-icon">🧪</div>
                    <div class="bar-segments" id="left-mana-bar"></div>
                </div>
            </div>
        </div>
    </div>
    <!-- Remove or comment out this script that automatically redirects after 10 seconds -->
    <!--
    <script>
        setTimeout(function() {
            window.location.href = "message.html";
        }, 10000); // 10000 milliseconds = 10 seconds
    </script>
    -->

    <!-- Add this script to check if the image is loading properly -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if the man element is visible
            const manElement = document.getElementById('man');
            
            // Create a test image to check if the image URL is valid
            const testImg = new Image();
            testImg.onload = function() {
                console.log("NPC image loaded successfully");
            };
            testImg.onerror = function() {
                console.error("Failed to load NPC image");
                // Fallback to a different image if the original doesn't load
                manElement.style.backgroundImage = "url('npcs chatacter design 1.png')";
                // Try alternative path formats if needed
                setTimeout(() => {
                    if (getComputedStyle(manElement).backgroundImage === 'none') {
                        manElement.style.backgroundImage = "url('npcs_chatacter_design_1.png')";
                    }
                }, 100);
            };
            testImg.src = 'npcs\ chatacter\ design\ 1.png';
        });
    </script>

</body>
</html>
