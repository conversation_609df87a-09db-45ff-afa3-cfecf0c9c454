body {
  margin: 0;
  background: #111;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}
* {
  margin: 0;
  padding: 0;
}

html, body {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: black;
  position: relative;
}

/* Back button styling */
.back-button {
  position: absolute;
  top: 20px;
  left: 20px;
  background-color: #00bfff;
  color: black;
  border: 3px solid #000;
  padding: 12px 20px;
  font-family: 'Press Start 2P', cursive;
  font-size: 0.8rem;
  cursor: pointer;
  border-radius: 25px;
  transition: all 0.2s ease;
  text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.5);
  box-shadow:
      inset 2px 2px 0 rgba(255, 255, 255, 0.3),
      inset -2px -2px 0 rgba(0, 0, 0, 0.3),
      0 4px 8px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  letter-spacing: 1px;
}

.back-button:hover {
  background-color: #1e90ff;
  transform: translateY(-2px);
  box-shadow:
      inset 2px 2px 0 rgba(255, 255, 255, 0.4),
      inset -2px -2px 0 rgba(0, 0, 0, 0.4),
      0 6px 12px rgba(0, 0, 0, 0.4);
}

.back-button:active {
  transform: translateY(0px);
  box-shadow:
      inset 2px 2px 0 rgba(255, 255, 255, 0.2),
      inset -2px -2px 0 rgba(0, 0, 0, 0.5),
      0 2px 4px rgba(0, 0, 0, 0.3);
}

canvas {
  display: block;
  width: 100vw;
  height: 100vh;
}
