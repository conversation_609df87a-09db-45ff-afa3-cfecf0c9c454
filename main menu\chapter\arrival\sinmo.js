// Load Sinmo transformation sound with multiple path attempts
let transformationSound;
let transformationSoundLoaded = false;
const soundPaths = [
    "../../Sound effects/Sinmo transformation .mp3",
    "../Sound effects/Sinmo transformation .mp3",
    "./Sound effects/Sinmo transformation .mp3",
    "../../../Sound effects/Sinmo transformation .mp3"
];

// First message sound
let firstMessageSound;

// Try to load the transformation sound from different paths
function loadTransformationSound() {
    let pathIndex = 0;

    function tryNextPath() {
        if (pathIndex >= soundPaths.length) {
            console.error('Could not load Sinmo transformation sound from any path');
            return;
        }

        transformationSound = new Audio(soundPaths[pathIndex]);
        transformationSound.preload = "auto";
        transformationSound.volume = 0.8;

        transformationSound.addEventListener('canplaythrough', () => {
            console.log(`Sinmo transformation sound loaded successfully from: ${soundPaths[pathIndex]}`);
            transformationSoundLoaded = true;
        });

        transformationSound.addEventListener('error', (e) => {
            console.log(`Failed to load from path ${pathIndex}: ${soundPaths[pathIndex]}`);
            pathIndex++;
            tryNextPath();
        });
    }

    tryNextPath();
}

// Initialize transformation sound loading
loadTransformationSound();

// Load first message sound
function loadFirstMessageSound() {
    firstMessageSound = new Audio('sinmo 1 sound.mp3');
    firstMessageSound.volume = 0.7;
    firstMessageSound.preload = 'auto';

    firstMessageSound.addEventListener('canplaythrough', () => {
        console.log('First message sound (sinmo 1 sound.mp3) loaded successfully');
    });

    firstMessageSound.addEventListener('error', (e) => {
        console.log('Failed to load first message sound (sinmo 1 sound.mp3):', e);
    });
}

// Function to play transformation sound
function playTransformationSound() {
    if (transformationSound) {
        console.log('Attempting to play transformation sound...');
        transformationSound.currentTime = 0;
        transformationSound.play().then(() => {
            console.log('Transformation sound played successfully');
        }).catch(error => {
            console.log('Could not play transformation sound:', error);
        });
    }
}

// Function to stop all audio
function stopAllAudio() {
    if (firstMessageSound && !firstMessageSound.paused) {
        firstMessageSound.pause();
        firstMessageSound.currentTime = 0;
        console.log('Stopped first message sound');
    }
    if (transformationSound && !transformationSound.paused) {
        transformationSound.pause();
        transformationSound.currentTime = 0;
        console.log('Stopped transformation sound');
    }
}

// Function to play first message sound
function playFirstMessageSound() {
    if (firstMessageSound) {
        // Stop all audio first to prevent multiple sounds playing
        stopAllAudio();
        console.log('Playing sinmo 1 sound.mp3 for first message');
        firstMessageSound.currentTime = 0;
        firstMessageSound.play().then(() => {
            console.log('First message sound played successfully');
        }).catch(error => {
            console.log('Could not play first message sound:', error);
        });
    }
}

document.addEventListener('DOMContentLoaded', () => {
    const dialogueBox = document.getElementById('dialogue-box');
    const dialogueText = document.getElementById('dialogue-text');
    const nextButton = document.getElementById('next-button');

    // Load first message sound
    loadFirstMessageSound();

    // Add click listener to enable audio on user interaction
    document.addEventListener('click', () => {
        if (transformationSound && !transformationSoundLoaded) {
            // Try to prime the audio for later playback
            transformationSound.play().then(() => {
                transformationSound.pause();
                transformationSound.currentTime = 0;
                console.log('Audio primed for later playback');
            }).catch(() => {
                // Ignore errors during priming
            });
        }
        // Also prime the first message sound
        if (firstMessageSound) {
            firstMessageSound.play().then(() => {
                firstMessageSound.pause();
                firstMessageSound.currentTime = 0;
                console.log('First message sound primed for later playback');
            }).catch(() => {
                // Ignore errors during priming
            });
        }
    }, { once: true });

    // Dialogue lines
    const dialogueLines = [
        "The Holy Relic!! This relic... they say it holds the essence of my past. Could it truly heal my spirit? Can I find my way back?"
    ];
    let currentDialogueIndex = 0;

    // Show dialogue with first message
    function showDialogue() {
        dialogueBox.classList.add('visible');
        // Play first message sound when dialogue appears
        playFirstMessageSound();
        displayNextDialogueLine();
    }

    // Hide dialogue
    function hideDialogue() {
        dialogueBox.classList.remove('visible');
    }

    // Display next dialogue line
    function displayNextDialogueLine() {
        if (currentDialogueIndex < dialogueLines.length) {
            // Display message instantly (no typewriter effect, per user preference)
            dialogueText.textContent = dialogueLines[currentDialogueIndex];
            currentDialogueIndex++;
            nextButton.textContent = currentDialogueIndex === dialogueLines.length ? "Close" : "Next";
        } else {
            hideDialogue();
            // Stop all audio before redirecting
            stopAllAudio();
            // Redirect to ending scene after dialogue completion
            setTimeout(() => {
                redirectToEndingScene();
            }, 1000); // Wait 1 second after dialogue closes
        }
    }

    // Event listener for next button
    nextButton.addEventListener('click', displayNextDialogueLine);

    // Show dialogue after short delay (1 second)
    setTimeout(showDialogue, 1000);
});

// Function to handle redirect to ending scene with black screen transition
function redirectToEndingScene() {
    console.log('Dialogue completed, redirecting to ending scene...');
    console.log('Transformation sound loaded:', transformationSoundLoaded);
    console.log('Transformation sound object exists:', !!transformationSound);

    // Stop all audio first, then play transformation sound
    stopAllAudio();
    // Play transformation sound immediately
    playTransformationSound();

    // Get the black screen overlay
    const blackScreen = document.getElementById('black-screen-overlay');
    if (blackScreen) {
        // Show and fade in the black screen
        blackScreen.style.display = 'block';
        setTimeout(() => {
            blackScreen.style.opacity = '1';
        }, 50);

        // Redirect after fade completes (extended time to let transformation sound play)
        setTimeout(() => {
            window.location.href = 'angel/angel.html';
        }, 3000); // 3 seconds to allow transformation sound to play during fade
    } else {
        // Fallback if no black screen overlay
        setTimeout(() => {
            window.location.href = 'angel/angel.html';
        }, 2000); // Still give time for sound even without black screen
    }
}
