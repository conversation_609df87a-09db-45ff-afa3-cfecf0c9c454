<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Audio Flow Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 5px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .log {
            background-color: #2a2a2a;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
            font-family: monospace;
            max-height: 200px;
            overflow-y: auto;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .info { color: #2196F3; }
        .navigation {
            background-color: #333;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Audio Flow Test</h1>
    
    <div class="test-section">
        <h2>Navigation Flow Test</h2>
        <div class="navigation">
            <h3>Test the Complete Flow:</h3>
            <p>1. <a href="index.html" target="_blank">Start from Loading Screen (index.html)</a></p>
            <p>2. Wait for loading to complete → Should redirect to game.html</p>
            <p>3. Click "Press Start" → Should go to main menu</p>
            <p>4. Audio should continue playing throughout</p>
        </div>
        
        <h3>Direct Page Tests:</h3>
        <button onclick="window.open('game.html', '_blank')">Test Game.html Directly</button>
        <button onclick="window.open('main menu/start.html', '_blank')">Test Main Menu Directly</button>
        <button onclick="window.open('main menu/chapter/chapter.html', '_blank')">Test Chapter Selection</button>
    </div>

    <div class="test-section">
        <h2>Audio System Test</h2>
        <button onclick="testAudioPaths()">Test All Audio Paths</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <div class="test-section">
        <h2>Expected Behavior</h2>
        <div style="background-color: #2a2a2a; padding: 15px; border-radius: 5px;">
            <h3>✅ What Should Happen (FIXED - No Audio Cutting):</h3>
            <ol>
                <li><strong>Loading Screen</strong> → Completes and redirects to game.html</li>
                <li><strong>Game.html</strong> → Press start audio begins playing (ONE instance only)</li>
                <li><strong>Main Menu</strong> → SAME audio continues seamlessly (no cutting, no restart)</li>
                <li><strong>Chapter Selection</strong> → SAME audio continues playing (no interruption)</li>
                <li><strong>Chapter Selected</strong> → Audio stops cleanly</li>
            </ol>

            <h3>🔧 New Implementation Features:</h3>
            <ul>
                <li><strong>Shared Audio Manager</strong> - One audio instance across all pages</li>
                <li><strong>State Persistence</strong> - Audio state saved in localStorage</li>
                <li><strong>Seamless Transitions</strong> - No audio cutting between pages</li>
                <li><strong>Smart Recovery</strong> - Audio resumes from exact position</li>
            </ul>
            
            <h3>🔧 Troubleshooting:</h3>
            <ul>
                <li>Check the audio status indicators in top-right corner of each page</li>
                <li>Open browser console (F12) to see detailed logs</li>
                <li>Click anywhere on the page if audio doesn't auto-play (browser policy)</li>
                <li>Make sure Press start.mp3 file exists in the root directory</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>Test Log</h2>
        <div id="log" class="log">
            <div class="info">🎵 Audio Flow Test Ready. Click buttons above to test...</div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '<div class="info">🔄 Log cleared.</div>';
        }

        function testAudioPaths() {
            log('🔄 Testing all press start audio paths...', 'info');
            
            const allPaths = [
                // From root directory
                'Press start.mp3',
                './Press start.mp3',
                
                // From main menu directory
                '../Press start.mp3',
                '../../Press start.mp3',
                
                // From chapter directory
                '../../Press start.mp3',
                '../../../Press start.mp3'
            ];
            
            allPaths.forEach((path, index) => {
                setTimeout(() => {
                    testSinglePath(path);
                }, index * 500); // Stagger tests
            });
        }

        function testSinglePath(path) {
            log(`🔄 Testing: ${path}`, 'info');
            
            const audio = new Audio(path);
            audio.volume = 0.1; // Low volume
            
            audio.addEventListener('canplaythrough', () => {
                log(`✅ SUCCESS: ${path} loaded`, 'success');
            });
            
            audio.addEventListener('error', (e) => {
                log(`❌ ERROR: ${path} failed - ${e.type}`, 'error');
            });
            
            // Try to play briefly
            audio.play().then(() => {
                log(`🔊 ${path} can play`, 'success');
                setTimeout(() => {
                    audio.pause();
                    audio.currentTime = 0;
                }, 200); // Play for 0.2 seconds
            }).catch(error => {
                log(`❌ ${path} playback failed: ${error.message}`, 'error');
            });
        }

        // Log current page location
        log('📁 Current page location: ' + window.location.href, 'info');
        log('📁 Testing from: ' + window.location.pathname.split('/').slice(0, -1).join('/'), 'info');
    </script>
</body>
</html>
