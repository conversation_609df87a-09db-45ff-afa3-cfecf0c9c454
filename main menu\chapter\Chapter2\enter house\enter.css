body {
  margin: 0;
  background: black;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

#ggame-container {
  position: relative;
  width: 800px;
  height: 700px;
  border: none;
  overflow: hidden;
}

#background {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 0;
  object-fit: cover; /* Makes image cover the entire container */
}
#housewife{
  position: absolute;
  width: 58px;
  height: 140px;
  left: 380px; /* horizontally centered like player */
  top: 230px;   /* vertical position where player stops */
  z-index: 1;

}
#player {
  position: absolute;
  width: 41px;
  height: 140px;
  left: 385px; /* Start center aligned */
  top: 480px;
  x: 50px;
  y: 450px;
  z-index: 3; /* in front of housewife */
}
#dialogue-box {
  position: absolute;
  top: 200px;
  left: 820px;
  width: 320px;
  padding: 16px;
  background: #fff;
  border: 3px solid #000;
  border-radius: 12px;
  z-index: 5;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  box-shadow: 4px 4px #888;
}

#dialogue-box::after {
  content: "";
  position: absolute;
  bottom: -15px;
  left: 20px;
  width: 0;
  height: 0;
  border: 10px solid transparent;
  border-top-color: #fff;
}

#dialogue-text {
  margin-bottom: 12px;
}

#next-btn, #continue-btn {
  padding: 6px 14px;
  font-size: 13px;
  font-weight: bold;
  background-color: #000;
  color: #fff;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  margin-right: 10px;
}

#choice-container {
  display: flex;
  gap: 15px;
  margin-top: 10px;
  justify-content: center;
}

/* Scroll popup styling */
#scroll-popup {
  position: absolute;
  height: 200px;
  top: 480px;
  left: 50%;
  transform: translateX(-50%);
  width: 400px;
  background: url('letter.png') center/cover;
  z-index: 200;
  opacity: 0;
  transition: opacity 0.6s ease-out;
}

#scroll-popup.show {
  opacity: 1;
}

#scroll-content {
  padding: 15px;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.letter-text {
  margin: 0;
  padding: 0 20px;
  font-size: 14px;
  line-height: 1.3;
  color: black;
  font-family: 'Press Start 2P', cursive;
  text-align: center;
}

.scroll-next-button {
  display: block;
  margin: 15px auto 0;
  background: #4CAF50;
  color: white;
  border: none;
  padding: 8px 18px;
  border-radius: 5px;
  cursor: pointer;
  font-weight: bold;
  font-size: 12px;
  font-family: 'Press Start 2P', cursive;
  box-shadow: 0 0 8px rgba(0,0,0,0.5);
}

.scroll-next-button:hover {
  background: #45a049;
}

#yes-btn, #no-btn {
  padding: 8px 16px;
  font-size: 13px;
  font-weight: bold;
  font-family: 'Press Start 2P', cursive;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

#yes-btn {
  background-color: #4CAF50;
  color: white;
}

#yes-btn:hover {
  background-color: #45a049;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0,0,0,0.3);
}

#no-btn {
  background-color: #f44336;
  color: white;
}

#no-btn:hover {
  background-color: #da190b;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0,0,0,0.3);
}

