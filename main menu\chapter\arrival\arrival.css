body {
    margin: 0;
    overflow: hidden; /* Hide scrollbars if elements go off-screen */
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh; /* Center the container vertically */
    background-color: #000; /* Black background for better contrast */
    width: 100%;
}

#game-container {
    position: relative;
    width: 100%; /* Full width */
    height: 100vh; /* Full viewport height */
    overflow: hidden; /* Ensure characters don't go outside the container */
}

#background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover; /* Ensures the background covers the container */
    image-rendering: crisp-edges; /* For pixel art sharpness */
    image-rendering: pixelated;
}

/* Make sure UI elements scale properly */
.info-box, .status-bars, .inventory {
    width: 100%;
    box-sizing: border-box;
}

/* Ensure inventory items fill their containers */
.item {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
}

#girl {
    position: absolute;
    bottom: 10px; /* Adjust based on your character's feet alignment with the ground */
    left: calc(50% - 40px); /* Center horizontally */
    width: 80px; /* Adjust as needed */
    height: auto;
    image-rendering: crisp-edges;
    image-rendering: pixelated;
}

#treasure {
    position: absolute;
    bottom: 250px; /* Adjust to be near the girl's hand/level */
    left: -10px; /* Start off-screen with the girl */
    width: 30px; /* Adjust size */
    height: 30px; /* Adjust size */
    image-rendering: crisp-edges;
    image-rendering: pixelated;
}

#demon {
    position: absolute;
    bottom: 1px; /* Adjust based on character's feet alignment */
    right: 120px; /* Start near the house on the right */
    width: 50px; /* Adjust size */
    height: 70px; /* Adjust size */
    image-rendering: crisp-edges;
    image-rendering: pixelated;
}
/* For smoother animations */
#girl, #treasure, #demon {
    transition: transform 0.1s ease-out;
    will-change: transform; /* Optimize for animation */
}

/* Responsive sizing for characters */
#girl {
    width: 80px; /* Adjust as needed */
    height: 190px;
}

#demon {
    width: 80px;
    height: 190px;
    bottom: 10px;
    right: 20px;
}

#treasure {
    width: 60px;
    height: auto;
    x: 400px;
    y: 600px;
}
#girl.stopped {
    animation: none !important;
    left: calc(40% - 30px) !important; /* Center position */
}
