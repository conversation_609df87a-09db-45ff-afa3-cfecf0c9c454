// Load Sinmo transformation sound with multiple path attempts
let transformationSound;
let transformationSoundLoaded = false;
const soundPaths = [
    "../../../Sound effects/Sinmo transformation .mp3",
    "../../Sound effects/Sinmo transformation .mp3",
    "../Sound effects/Sinmo transformation .mp3",
    "./Sound effects/Sinmo transformation .mp3"
];

// First message video
let firstMessageVideo;

// Second message video
let secondMessageVideo;

// Third message video
let thirdMessageVideo;

// Try to load the transformation sound from different paths
function loadTransformationSound() {
    let pathIndex = 0;

    function tryNextPath() {
        if (pathIndex >= soundPaths.length) {
            console.error('Could not load Sinmo transformation sound from any path');
            return;
        }

        transformationSound = new Audio(soundPaths[pathIndex]);
        transformationSound.preload = "auto";
        transformationSound.volume = 0.8;

        transformationSound.addEventListener('canplaythrough', () => {
            console.log(`Sinmo transformation sound loaded successfully from: ${soundPaths[pathIndex]}`);
            transformationSoundLoaded = true;
        });

        transformationSound.addEventListener('error', (e) => {
            console.log(`Failed to load from path ${pathIndex}: ${soundPaths[pathIndex]}`);
            pathIndex++;
            tryNextPath();
        });
    }

    tryNextPath();
}

// Function to play transformation sound
function playTransformationSound() {
    console.log('playTransformationSound called on angel page');
    console.log('transformationSound exists:', !!transformationSound);
    console.log('transformationSoundLoaded:', transformationSoundLoaded);

    if (transformationSound) {
        console.log('Playing transformation sound on angel page...');
        transformationSound.currentTime = 0;
        transformationSound.volume = 0.8; // Ensure volume is set
        transformationSound.play().then(() => {
            console.log('Transformation sound played successfully on angel page');
        }).catch(error => {
            console.log('Could not play transformation sound on angel page:', error);
            console.log('Error details:', error);

            // Try to reload and play again
            setTimeout(() => {
                console.log('Retrying transformation sound...');
                loadTransformationSound();
                setTimeout(() => {
                    if (transformationSound) {
                        transformationSound.play().catch(e => console.log('Retry failed:', e));
                    }
                }, 1000);
            }, 500);
        });
    } else {
        console.log('Transformation sound not loaded on angel page, trying to load...');
        loadTransformationSound();
    }
}

// Initialize transformation sound loading
loadTransformationSound();

// Load first message video
function loadFirstMessageVideo() {
    firstMessageVideo = document.createElement('video');
    firstMessageVideo.src = '11.mp4';
    firstMessageVideo.volume = 0.7;
    firstMessageVideo.preload = 'auto';
    firstMessageVideo.style.display = 'none'; // Hidden video element

    firstMessageVideo.addEventListener('canplaythrough', () => {
        console.log('First message video (11.mp4) loaded successfully');
    });

    firstMessageVideo.addEventListener('error', (e) => {
        console.log('Failed to load first message video (11.mp4):', e);
    });

    // Add to document
    document.body.appendChild(firstMessageVideo);
}

// Load second message video
function loadSecondMessageVideo() {
    secondMessageVideo = document.createElement('video');
    secondMessageVideo.src = '12.mp4';
    secondMessageVideo.volume = 0.7;
    secondMessageVideo.preload = 'auto';
    secondMessageVideo.style.display = 'none'; // Hidden video element

    secondMessageVideo.addEventListener('canplaythrough', () => {
        console.log('Second message video (12.mp4) loaded successfully');
    });

    secondMessageVideo.addEventListener('error', (e) => {
        console.log('Failed to load second message video (12.mp4):', e);
    });

    // Add to document
    document.body.appendChild(secondMessageVideo);
}

// Load third message video
function loadThirdMessageVideo() {
    thirdMessageVideo = document.createElement('video');
    thirdMessageVideo.src = '13.mp4';
    thirdMessageVideo.volume = 0.7;
    thirdMessageVideo.preload = 'auto';
    thirdMessageVideo.style.display = 'none'; // Hidden video element

    thirdMessageVideo.addEventListener('canplaythrough', () => {
        console.log('Third message video (13.mp4) loaded successfully');
    });

    thirdMessageVideo.addEventListener('error', (e) => {
        console.log('Failed to load third message video (13.mp4):', e);
    });

    // Add to document
    document.body.appendChild(thirdMessageVideo);
}

// Function to stop all videos
function stopAllVideos() {
    if (firstMessageVideo && !firstMessageVideo.paused) {
        firstMessageVideo.pause();
        firstMessageVideo.currentTime = 0;
        console.log('Stopped first message video');
    }
    if (secondMessageVideo && !secondMessageVideo.paused) {
        secondMessageVideo.pause();
        secondMessageVideo.currentTime = 0;
        console.log('Stopped second message video');
    }
    if (thirdMessageVideo && !thirdMessageVideo.paused) {
        thirdMessageVideo.pause();
        thirdMessageVideo.currentTime = 0;
        console.log('Stopped third message video');
    }
}

// Function to play first message video
function playFirstMessageVideo() {
    if (firstMessageVideo) {
        // Stop all videos first to prevent multiple videos playing
        stopAllVideos();
        console.log('Playing 11.mp4 for first message');
        firstMessageVideo.currentTime = 0;
        firstMessageVideo.play().then(() => {
            console.log('First message video played successfully');
        }).catch(error => {
            console.log('Could not play first message video:', error);
        });
    }
}

// Function to play second message video
function playSecondMessageVideo() {
    if (secondMessageVideo) {
        // Stop all videos first to prevent multiple videos playing
        stopAllVideos();
        console.log('Playing 12.mp4 for second message');
        secondMessageVideo.currentTime = 0;
        secondMessageVideo.play().then(() => {
            console.log('Second message video played successfully');
        }).catch(error => {
            console.log('Could not play second message video:', error);
        });
    }
}

// Function to play third message video
function playThirdMessageVideo() {
    if (thirdMessageVideo) {
        // Stop all videos first to prevent multiple videos playing
        stopAllVideos();
        console.log('Playing 13.mp4 for third message');
        thirdMessageVideo.currentTime = 0;
        thirdMessageVideo.play().then(() => {
            console.log('Third message video played successfully');
        }).catch(error => {
            console.log('Could not play third message video:', error);
        });
    }
}

document.addEventListener('DOMContentLoaded', () => {
    console.log('Angel page loaded, setting up transformation sound...');

    // Load first message video
    loadFirstMessageVideo();

    // Load second message video
    loadSecondMessageVideo();

    // Load third message video
    loadThirdMessageVideo();

    // Try to play transformation sound after a delay
    setTimeout(() => {
        console.log('Attempting to play transformation sound on angel page...');
        playTransformationSound();
    }, 500); // Longer delay to ensure sound is loaded

    // Add click listener to try playing sound on user interaction (fallback)
    document.addEventListener('click', () => {
        console.log('User clicked on angel page, trying to play transformation sound...');
        playTransformationSound();
    }, { once: true });

    // Also try to play on any key press
    document.addEventListener('keydown', () => {
        console.log('User pressed key on angel page, trying to play transformation sound...');
        playTransformationSound();
    }, { once: true });

    // Handle black screen fade-out on page load
    const blackScreen = document.getElementById('black-screen-overlay');
    if (blackScreen) {
        setTimeout(() => {
            blackScreen.style.opacity = '0';
            // Remove the overlay after fade completes
            setTimeout(() => {
                blackScreen.remove();
            }, 2000);
        }, 500);
    }

    const dialogueBox = document.getElementById('dialogue-box');
    const dialogueText = document.getElementById('dialogue-text');
    const nextButton = document.getElementById('next-button');

    // Dialogue lines
    const dialogueLines = [
        "Thank you, brave soul. I was lost in darkness, consumed by my own turmoil...",
        "I can feel the light returning to me, the warmth of hope...",
        "Let us move forward together. Thank you for your kindness..."
    ];
    let currentDialogueIndex = 0;

    // Show dialogue with first message
    function showDialogue() {
        dialogueBox.classList.add('visible');
        // Play first message video when dialogue appears
        playFirstMessageVideo();
        displayNextDialogueLine();
    }

    // Hide dialogue
    function hideDialogue() {
        dialogueBox.classList.remove('visible');
    }

    // Display next dialogue line
    function displayNextDialogueLine() {
        if (currentDialogueIndex < dialogueLines.length) {
            // Display message instantly (no typewriter effect, per user preference)
            dialogueText.textContent = dialogueLines[currentDialogueIndex];

            // Play video when specific messages appear
            if (currentDialogueIndex === 1 && secondMessageVideo) {
                // Play 12.mp4 when second message appears
                console.log('Playing 12.mp4 for second message');
                secondMessageVideo.currentTime = 0;
                secondMessageVideo.play().then(() => {
                    console.log('Second message video (12.mp4) played successfully');
                }).catch(error => {
                    console.log('Could not play second message video (12.mp4):', error);
                });
            } else if (currentDialogueIndex === 2 && thirdMessageVideo) {
                // Play 13.mp4 when third message appears
                console.log('Playing 13.mp4 for third message');
                thirdMessageVideo.currentTime = 0;
                thirdMessageVideo.play().then(() => {
                    console.log('Third message video (13.mp4) played successfully');
                }).catch(error => {
                    console.log('Could not play third message video (13.mp4):', error);
                });
            }

            currentDialogueIndex++;
            nextButton.textContent = currentDialogueIndex === dialogueLines.length ? "Close" : "Next";
        } else {
            hideDialogue();
            // Stop all videos before redirecting
            stopAllVideos();
            // Redirect to ending scene after dialogue completion
            setTimeout(() => {
                redirectToEndingScene();
            }, 1000); // Wait 1 second after dialogue closes
        }
    }

    // Event listener for next button
    nextButton.addEventListener('click', () => {
        // Stop all videos before advancing to next message
        stopAllVideos();
        displayNextDialogueLine();
    });

    // Show dialogue after short delay (1 second)
    setTimeout(showDialogue, 1000);
});

// Function to handle redirect to ending scene with black screen transition
function redirectToEndingScene() {
    console.log('Angel dialogue completed, redirecting to ending scene...');

    // Create black screen overlay for transition
    const fadeOverlay = document.createElement('div');
    fadeOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: black;
        opacity: 0;
        z-index: 10000;
        transition: opacity 1.5s ease-in-out;
        pointer-events: none;
    `;
    document.body.appendChild(fadeOverlay);

    // Trigger fade in
    setTimeout(() => {
        fadeOverlay.style.opacity = '1';
    }, 50);

    // Redirect after fade completes
    setTimeout(() => {
        window.location.href = '../../../ending scene/index.html';
    }, 1800); // 1.8 seconds to allow fade to complete
}