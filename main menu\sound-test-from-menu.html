<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sound Test from Main Menu Directory</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #222;
            color: white;
        }
        button {
            padding: 10px 20px;
            margin: 10px;
            font-size: 16px;
            cursor: pointer;
        }
        #status {
            background: #333;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .info { color: #2196F3; }
    </style>
</head>
<body>
    <h1>🔊 Sound Test from Main Menu Directory</h1>
    <p>Testing sound paths from the main menu folder perspective.</p>
    
    <button id="testBtn1">Test: ../Sound effects/Click menu and settings .mp3</button><br>
    <button id="testBtn2">Test: ../Sound effects/click-menu.mp3</button><br>
    <button id="testBtn3">Test: ./Sound effects/Click menu and settings .mp3</button><br>
    <button id="testBtn4">Test: Sound effects/Click menu and settings .mp3</button><br>
    <button id="clearBtn">Clear Log</button>

    <div id="status">
        <div class="info">🔄 Click a button to test sound loading...</div>
    </div>

    <script>
        const statusDiv = document.getElementById('status');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';
            statusDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            statusDiv.scrollTop = statusDiv.scrollHeight;
        }

        function testSinglePath(path) {
            log(`🔄 Testing path: ${path}`, 'info');
            
            const audio = new Audio(path);
            audio.volume = 0.7;
            
            audio.addEventListener('canplaythrough', () => {
                log(`✅ SUCCESS: Sound loaded from ${path}`, 'success');
            });
            
            audio.addEventListener('error', (e) => {
                log(`❌ ERROR: Failed to load from ${path} - ${e.type}`, 'error');
            });
            
            // Try to play
            audio.play().then(() => {
                log(`🔊 Sound played successfully from ${path}`, 'success');
            }).catch(error => {
                log(`❌ Playback failed: ${error.message}`, 'error');
            });
        }

        // Event listeners
        document.getElementById('testBtn1').addEventListener('click', () => {
            testSinglePath('../Sound effects/Click menu and settings .mp3');
        });

        document.getElementById('testBtn2').addEventListener('click', () => {
            testSinglePath('../Sound effects/click-menu.mp3');
        });

        document.getElementById('testBtn3').addEventListener('click', () => {
            testSinglePath('./Sound effects/Click menu and settings .mp3');
        });

        document.getElementById('testBtn4').addEventListener('click', () => {
            testSinglePath('Sound effects/Click menu and settings .mp3');
        });

        document.getElementById('clearBtn').addEventListener('click', () => {
            statusDiv.innerHTML = '<div class="info">🔄 Log cleared. Click a button to test sound loading...</div>';
        });

        // Initial info
        log('🎵 Sound Debug Test Ready from Main Menu Directory', 'info');
        log('📁 Current page location: ' + window.location.href, 'info');
    </script>
</body>
</html>
