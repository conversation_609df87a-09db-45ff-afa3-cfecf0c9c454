<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pixel Art Dialogue</title>
    <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="angel.css">
</head>
<body>
    <!-- Black screen overlay for fade-in transition -->
    <div id="black-screen-overlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: black; z-index: 10000; opacity: 1; transition: opacity 2s ease-out; pointer-events: none;"></div>

    <!-- Fixed Background -->
    <img id="background" src="img2.png" alt="Pixel Art Background">
    
    <!-- Game Container -->
    <div id="game-container">
        <!-- Dialogue Box -->
        <div id="dialogue-box" class="hidden">
            <p id="dialogue-text"></p>
            <button id="next-button">Next</button>
        </div>
    </div>

    <script src="angel.js"></script>
</body>
</html>