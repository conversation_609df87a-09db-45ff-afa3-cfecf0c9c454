// Sound manager for menu clicks
let menuClickSound;
const menuSoundPaths = [
    "../../Sound effects/Click menu and settings .mp3",
    "../Sound effects/Click menu and settings .mp3",
    "./Sound effects/Click menu and settings .mp3",
    "../../Sound effects/click-menu.mp3",
    "../Sound effects/click-menu.mp3",
    "./Sound effects/click-menu.mp3"
];

// Load menu click sound
function loadMenuClickSound() {
    let pathIndex = 0;

    function tryNextPath() {
        if (pathIndex >= menuSoundPaths.length) {
            console.error('Could not load menu click sound from any path');
            return;
        }

        menuClickSound = new Audio(menuSoundPaths[pathIndex]);
        menuClickSound.preload = "auto";
        menuClickSound.volume = 0.7;

        menuClickSound.addEventListener('canplaythrough', () => {
            console.log(`Menu click sound loaded successfully from: ${menuSoundPaths[pathIndex]}`);
        });

        menuClickSound.addEventListener('error', (e) => {
            console.log(`Failed to load menu sound from path ${pathIndex}: ${menuSoundPaths[pathIndex]}`);
            pathIndex++;
            tryNextPath();
        });
    }

    tryNextPath();
}

// Play menu click sound
function playMenuClickSound() {
    if (menuClickSound) {
        menuClickSound.currentTime = 0;
        menuClickSound.play().catch(error => {
            console.log('Could not play menu click sound:', error);
        });
    }
}

document.addEventListener('DOMContentLoaded', function() {
  // Load menu click sound
  loadMenuClickSound();

  const selectedCharacter = localStorage.getItem('selectedCharacter');
  if (selectedCharacter) {
    document.getElementById('playerCharacter').src = selectedCharacter;
  } else {
    // Ensure Frame 11 is the default when no character is selected
    document.getElementById('playerCharacter').src = 'img/Frame 11.png';
  }

  // Add click sound to customize button
  const customizeButton = document.querySelector('.button-link');
  if (customizeButton) {
    customizeButton.addEventListener('click', function() {
      playMenuClickSound();
    });
  }
});

function saveCharacter() {
  playMenuClickSound(); // Play click sound when saving character

  const currentCharacter = document.getElementById('playerCharacter').src;
  localStorage.setItem('savedCharacter', currentCharacter);

  // Clear the selected character so user can select again
  localStorage.removeItem('selectedCharacter');

  // Create and show popup
  const popup = document.createElement('div');
  popup.className = 'popup';
  popup.textContent = 'Character saved successfully!';
  document.body.appendChild(popup);

  // Show popup
  popup.style.display = 'block';

  // Hide popup and redirect after delay
  setTimeout(function() {
    popup.style.display = 'none';
    window.location.href = '../start.html'; // Return to main menu
  }, 1500);
}

// Fix redirect after saving character
setTimeout(function() {
  popup.style.display = 'none';
  window.location.href = '../start.html'; // Return to main menu
}, 1500);


