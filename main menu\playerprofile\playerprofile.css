* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
  margin: 0;
  background-color: #000;
  font-family:'Press Start 2P', cursive;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  color: white;
  font-size: 24px;
  position: relative;
}

/* Back button styling */
.back-button {
  position: absolute;
  top: 20px;
  left: 20px;
  background-color: #00bfff;
  color: black;
  border: 3px solid #000;
  padding: 12px 20px;
  font-family: 'Press Start 2P', cursive;
  font-size: 0.8rem;
  cursor: pointer;
  border-radius: 25px;
  transition: all 0.2s ease;
  text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.5);
  box-shadow:
      inset 2px 2px 0 rgba(255, 255, 255, 0.3),
      inset -2px -2px 0 rgba(0, 0, 0, 0.3),
      0 4px 8px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  letter-spacing: 1px;
}

.back-button:hover {
  background-color: #1e90ff;
  transform: translateY(-2px);
  box-shadow:
      inset 2px 2px 0 rgba(255, 255, 255, 0.4),
      inset -2px -2px 0 rgba(0, 0, 0, 0.4),
      0 6px 12px rgba(0, 0, 0, 0.4);
}

.back-button:active {
  transform: translateY(0px);
  box-shadow:
      inset 2px 2px 0 rgba(255, 255, 255, 0.2),
      inset -2px -2px 0 rgba(0, 0, 0, 0.5),
      0 2px 4px rgba(0, 0, 0, 0.3);
}

.profile-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40px;
  transform: scale(1.4);
}

.top-section {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  gap: 50px;
}

.profile-section .image-frame {
  position: relative;
  width: 280px;
  height: 230px;
  background: #333;
  display: inline-block;
  overflow: visible;
  border: 12px solid transparent;
  border-image: url('./img/box2\ 6.png') 10 fill stretch;
  padding: 4px; /* Add some padding inside the border */
}

.image-frame img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  object-position: center;
}

.edit-btn {
  position: absolute;
  bottom: -16px;
  right: -16px;
  background: limegreen;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  font-size: 18px;
  cursor: pointer;
  transform: translate(0, 0);
  z-index: 10;
}

.stats-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 380px; /* Increased width for longer info boxes */
}

.bar-group {
  display: flex;
  gap: 18px;
  margin-bottom: 12px;
}

.bar-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
  padding-left: 24px;
  justify-content: center;
  height: 30px; /* Reduced height for shorter bars */
}

.bar-segments {
  display: flex;
  gap: 3px;
  padding: 4px; /* Reduced padding */
  background: rgba(0, 0, 0, 0.7);
  border: 2px solid #444;
  border-radius: 4px;
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.5);
  align-items: center;
}

.bar-segment {
  width: 10px; /* Smaller squares */
  height: 10px; /* Smaller squares */
  background: #222;
  border: 1px solid #000;
  position: relative;
  border-radius: 2px;
  overflow: hidden;
}


.health .bar-segment {
  background: linear-gradient(to bottom, #3a0000, #222);
}

.health .bar-segment.active {
  background: linear-gradient(to bottom, #ff0000, #aa0000);
  box-shadow: 0 0 6px rgba(255, 0, 0, 0.8);
}

.mana .bar-segment {
  background: linear-gradient(to bottom, #00203a, #222);
}

.mana .bar-segment.active {
  background: linear-gradient(to bottom, #00a8ff, #0062aa);
  box-shadow: 0 0 6px rgba(0, 168, 255, 0.8);
}

.health .bar-segments {
  box-shadow: inset 0 0 8px rgba(255, 0, 0, 0.3);
}

.mana .bar-segments {
  box-shadow: inset 0 0 8px rgba(0, 168, 255, 0.3);
}

.bar-icon {
  position: absolute;
  font-size: 16px; /* Slightly smaller icon */
  z-index: 3;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  filter: drop-shadow(0 0 2px currentColor);
}

.health .bar-icon {
  color: red;
}

.mana .bar-icon {
  color: dodgerblue;
}

@keyframes segment-fill {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.bar-segment.active {
  animation: segment-fill 0.3s ease-out forwards;
  animation-delay: calc(var(--segment-index) * 0.1s);
}

.info-box {
  background: black;
  border: 6px solid transparent;
  padding: 8px 16px;
  font-size: 14px;
  width: 380px;
  border-image: url('img/border.png') 16 fill stretch;
  color: white;
  text-align: center;
  margin: 5px 0;
}

.info-box input {
  background: transparent;
  border: none;
  color: white;
  font-family: 'Press Start 2P', cursive;
  font-size: 14px;
  width: 100%;
  text-align: center;
  outline: none;
}

.info-box input::placeholder {
  color: #888;
}

.inventory {
  display: flex;
  gap: 24px; 
  padding: 10px; 
}

.item {
  width: 70px;
  height: 70px;
  background: #111;
  border: 2px solid white;
  position: relative;
  transition: transform 0.2s ease; 
}

.item:hover {
  transform: scale(1.05); 
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.item img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  object-position: center;
  display: none;
}

.item img[src]:not([src=""]) {
  display: block;
}

.item.empty {
  background: #111;
} 